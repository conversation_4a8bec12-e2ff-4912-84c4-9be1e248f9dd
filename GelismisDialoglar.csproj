<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net472</TargetFramework>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="TaleWorlds.Core">
      <HintPath>..\..\bin\Win64_Shipping_Client\TaleWorlds.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade">
      <HintPath>..\..\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.CampaignSystem">
      <HintPath>..\..\bin\Win64_Shipping_Client\TaleWorlds.CampaignSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.Library">
      <HintPath>..\..\bin\Win64_Shipping_Client\TaleWorlds.Library.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.Localization">
      <HintPath>..\..\bin\Win64_Shipping_Client\TaleWorlds.Localization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.ObjectSystem">
      <HintPath>..\..\bin\Win64_Shipping_Client\TaleWorlds.ObjectSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>

  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="copy &quot;$(TargetPath)&quot; &quot;$(ProjectDir)bin\Win64_Shipping_Client\&quot;" />
  </Target>

</Project>