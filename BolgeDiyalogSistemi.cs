using TaleWorlds.CampaignSystem;
using TaleWorlds.CampaignSystem.Conversation;
using TaleWorlds.CampaignSystem.Settlements;
using TaleWorlds.Core;
using TaleWorlds.Library;
using TaleWorlds.CampaignSystem.Actions;
using TaleWorlds.CampaignSystem.CharacterDevelopment;

namespace GelismisDialoglar
{
    public class BolgeDiyalogSistemi
    {
        private readonly string[] buyukSehirSelamlamalari = {
            "Büyük şehrimize hoş geldiniz, {PLAYER.NAME}! Burada her şey bulunur!",
            "Merhaba {PLAYER.NAME}! Şehrimizin kozmopolit atmosferini seveceksiniz!",
            "Hoş geldiniz, {PLAYER.NAME}! Büyük şehrin hareketli yaşamına katılın!",
            "<PERSON><PERSON><PERSON>, {PLAYER.NAME}! Şehrimiz sizin gibi önemli misafirleri ağırlamaktan onur duyar!",
            "Merhaba {PLAYER.NAME}! <PERSON><PERSON><PERSON>ük şehrimizde aradığınız her şeyi bulacaksınız!"
        };

        private readonly string[] kucukKasabaSelamlamalari = {
            "Küçük kasabamıza hoş geldiniz, {PLAYER.NAME}! Burada herkes birbirini tanır!",
            "Merhaba {PLAYER.NAME}! Kasabamızın sıcak atmosferini seveceksiniz!",
            "Hoş geldin, {PLAYER.NAME}! Kasabamız sizin gibi misafirleri sever!",
            "Selamlar, {PLAYER.NAME}! Küçük ama sıcak kasabamıza hoş geldiniz!",
            "Merhaba {PLAYER.NAME}! Kasabamızın huzurlu ortamında dinlenin!"
        };

        private readonly string[] koySelamlamalari = {
            "Köyümüze hoş geldiniz, {PLAYER.NAME}! Mütevazı ama sıcak evimiz!",
            "Merhaba efendim! Köyümüz sizin gibi büyük misafirleri ağırlamaktan şeref duyar!",
            "Hoş geldin, {PLAYER.NAME}! Köyümüzün sade güzelliğini seveceksiniz!",
            "Selamlar, {PLAYER.NAME}! Köyümüzde huzur ve barış var!",
            "Merhaba {PLAYER.NAME}! Köyümüzün doğal güzelliği sizin için!"
        };

        private readonly string[] kaleSelamlamalari = {
            "Kaleye hoş geldiniz, {PLAYER.NAME}! Burada güvenlik en üst seviyede!",
            "Merhaba komutanım! Kalemizin sağlam duvarları sizi koruyor!",
            "Hoş geldiniz, {PLAYER.NAME}! Kale sizin gibi savaşçıları selamlıyor!",
            "Selamlar, {PLAYER.NAME}! Kalemizin askeri disiplini takdire şayan!",
            "Merhaba {PLAYER.NAME}! Kale duvarları sizin gibi kahramanlar için açık!"
        };

        private readonly string[] sinirBolgesiSelamlamalari = {
            "Sınır bölgesine hoş geldiniz, {PLAYER.NAME}! Burada dikkatli olun!",
            "Merhaba {PLAYER.NAME}! Sınır bölgesi tehlikeli ama sizin için sorun değil!",
            "Hoş geldin, {PLAYER.NAME}! Sınırda her an her şey olabilir!",
            "Selamlar, {PLAYER.NAME}! Sınır bölgesinde tetikte kalın!",
            "Merhaba {PLAYER.NAME}! Sınır bölgesi sizin gibi cesurlar için!"
        };

        private readonly string[] ticaretMerkeziSelamlamalari = {
            "Ticaret merkezine hoş geldiniz, {PLAYER.NAME}! Burada altın akar!",
            "Merhaba {PLAYER.NAME}! Ticaret merkezi sizin gibi girişimcileri seviyor!",
            "Hoş geldiniz, {PLAYER.NAME}! Burada en iyi fırsatlar sizi bekliyor!",
            "Selamlar, {PLAYER.NAME}! Ticaret merkezi karlı işler için ideal!",
            "Merhaba {PLAYER.NAME}! Burada para kazanmak kolay!"
        };

        public void AddDialogs(CampaignGameStarter campaignStarter)
        {
            this.AddBuyukSehirDialoglari(campaignStarter);
            this.AddKucukKasabaDialoglari(campaignStarter);
            this.AddKoyDialoglari(campaignStarter);
            this.AddKaleDialoglari(campaignStarter);
            this.AddSinirBolgesiDialoglari(campaignStarter);
            this.AddTicaretMerkeziDialoglari(campaignStarter);
        }

        private void AddBuyukSehirDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < buyukSehirSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"bolge_buyuk_sehir_{i}", "start", "bolge_secenekleri",
                    buyukSehirSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsBuyukSehir() &&
                          MBRandom.RandomInt(0, buyukSehirSelamlamalari.Length) == index,
                    null);
            }

            // Büyük şehir seçenekleri
            campaignStarter.AddPlayerLine("buyuk_sehir_soru_1", "bolge_secenekleri", "buyuk_sehir_cevap_1",
                "Bu şehir gerçekten büyük!", 
                () => IsBuyukSehir(), null);

            campaignStarter.AddDialogLine("buyuk_sehir_cevap_1", "buyuk_sehir_cevap_1", "bolge_secenekleri",
                "Evet! Binlerce insan yaşıyor burada. Her türlü ihtiyacınızı karşılayabiliriz!",
                null, () => GiveBolgeOdulu(30));

            campaignStarter.AddPlayerLine("bolge_cikis", "bolge_secenekleri", "close_window",
                "Teşekkürler!", null, null);
        }

        private void AddKucukKasabaDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < kucukKasabaSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"bolge_kucuk_kasaba_{i}", "start", "bolge_secenekleri",
                    kucukKasabaSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsKucukKasaba() &&
                          MBRandom.RandomInt(0, kucukKasabaSelamlamalari.Length) == index,
                    null);
            }

            // Küçük kasaba seçenekleri
            campaignStarter.AddPlayerLine("kucuk_kasaba_soru_1", "bolge_secenekleri", "kucuk_kasaba_cevap_1",
                "Kasabanız çok huzurlu!", 
                () => IsKucukKasaba(), null);

            campaignStarter.AddDialogLine("kucuk_kasaba_cevap_1", "kucuk_kasaba_cevap_1", "bolge_secenekleri",
                "Teşekkürler! Küçük ama mutlu bir topluluk. Herkes birbirine yardım eder.",
                null, () => GiveBolgeOdulu(20));
        }

        private void AddKoyDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < koySelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"bolge_koy_{i}", "start", "bolge_secenekleri",
                    koySelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsKoy() &&
                          MBRandom.RandomInt(0, koySelamlamalari.Length) == index,
                    null);
            }

            // Köy seçenekleri
            campaignStarter.AddPlayerLine("koy_soru_1", "bolge_secenekleri", "koy_cevap_1",
                "Köyünüz çok güzel!", 
                () => IsKoy(), null);

            campaignStarter.AddDialogLine("koy_cevap_1", "koy_cevap_1", "bolge_secenekleri",
                "Çok naziksiniz! Doğayla iç içe yaşıyoruz. Sade ama mutlu bir hayat.",
                null, () => GiveBolgeOdulu(15));
        }

        private void AddKaleDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < kaleSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"bolge_kale_{i}", "start", "bolge_secenekleri",
                    kaleSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsKale() &&
                          MBRandom.RandomInt(0, kaleSelamlamalari.Length) == index,
                    null);
            }

            // Kale seçenekleri
            campaignStarter.AddPlayerLine("kale_soru_1", "bolge_secenekleri", "kale_cevap_1",
                "Kale çok sağlam görünüyor!", 
                () => IsKale(), null);

            campaignStarter.AddDialogLine("kale_cevap_1", "kale_cevap_1", "bolge_secenekleri",
                "Elbette! Bu duvarlar yüzyıllardır ayakta. Hiçbir düşman geçemez!",
                null, () => GiveBolgeOdulu(35));
        }

        private void AddSinirBolgesiDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < sinirBolgesiSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"bolge_sinir_{i}", "start", "bolge_secenekleri",
                    sinirBolgesiSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsSinirBolgesi() &&
                          MBRandom.RandomInt(0, sinirBolgesiSelamlamalari.Length) == index,
                    null);
            }

            // S��nır bölgesi seçenekleri
            campaignStarter.AddPlayerLine("sinir_soru_1", "bolge_secenekleri", "sinir_cevap_1",
                "Burada tehlikeli mi?", 
                () => IsSinirBolgesi(), null);

            campaignStarter.AddDialogLine("sinir_cevap_1", "sinir_cevap_1", "bolge_secenekleri",
                "Evet, sınır bölgesi. Eşkıyalar ve düşman askerleri olabilir. Dikkatli olun!",
                null, () => GiveBolgeOdulu(40));
        }

        private void AddTicaretMerkeziDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < ticaretMerkeziSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"bolge_ticaret_{i}", "start", "bolge_secenekleri",
                    ticaretMerkeziSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsTicaretMerkezi() &&
                          MBRandom.RandomInt(0, ticaretMerkeziSelamlamalari.Length) == index,
                    null);
            }

            // Ticaret merkezi seçenekleri
            campaignStarter.AddPlayerLine("ticaret_soru_1", "bolge_secenekleri", "ticaret_cevap_1",
                "Ticaret nasıl gidiyor?", 
                () => IsTicaretMerkezi(), null);

            campaignStarter.AddDialogLine("ticaret_cevap_1", "ticaret_cevap_1", "bolge_secenekleri",
                "Harika! Kervanlar sürekli gelip gidiyor. Para kazanmak için ideal yer!",
                null, () => GiveBolgeOdulu(25));
        }

        // Bölge kontrol metodları
        private bool IsBuyukSehir()
        {
            var settlement = Settlement.CurrentSettlement;
            return settlement?.IsTown == true && settlement.Town?.Prosperity > 5000;
        }

        private bool IsKucukKasaba()
        {
            var settlement = Settlement.CurrentSettlement;
            return settlement?.IsTown == true && settlement.Town?.Prosperity <= 5000;
        }

        private bool IsKoy()
        {
            var settlement = Settlement.CurrentSettlement;
            return settlement?.IsVillage == true;
        }

        private bool IsKale()
        {
            var settlement = Settlement.CurrentSettlement;
            return settlement?.IsCastle == true;
        }

        private bool IsSinirBolgesi()
        {
            var settlement = Settlement.CurrentSettlement;
            if (settlement == null) return false;

            // Sınır bölgesi kontrolü - düşman topraklarına yakın
            foreach (var faction in Campaign.Current.Factions)
            {
                if (faction != Hero.MainHero.MapFaction && 
                    FactionManager.IsAtWarAgainstFaction(Hero.MainHero.MapFaction, faction))
                {
                    foreach (var enemySettlement in faction.Settlements)
                    {
                        float distance = settlement.Position2D.Distance(enemySettlement.Position2D);
                        if (distance < 50f) // 50 birim yakınlık
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        private bool IsTicaretMerkezi()
        {
            var settlement = Settlement.CurrentSettlement;
            return settlement?.IsTown == true && settlement.Town?.MarketData?.GetPrice(DefaultItems.Grain) > 0;
        }

        private void GiveBolgeOdulu(int xp)
        {
            if (xp > 0)
            {
                Hero.MainHero.AddSkillXp(DefaultSkills.Charm, xp);
            }
        }
    }
}