using TaleWorlds.CampaignSystem;
using TaleWorlds.CampaignSystem.Conversation;
using TaleWorlds.Core;
using TaleWorlds.Library;
using TaleWorlds.CampaignSystem.Actions;
using TaleWorlds.CampaignSystem.CharacterDevelopment;

namespace GelismisDialoglar
{
    public class KulturDiyalogSistemi
    {
        private readonly string[] empireSelamlamalari = {
            "Selam<PERSON>, {PLAYER.NAME}. İmparatorluğun gücü sizinle olsun.",
            "<PERSON><PERSON> geldiniz, {PLAYER.NAME}. İmparatorluk sizi selamlıyor.",
            "Merhaba {PLAYER.NAME}. İmparatorluğun adaleti sizinle olsun.",
            "<PERSON><PERSON><PERSON>, asil {PLAYER.NAME}. İmparatorluk sizinle gurur duyuyor.",
            "<PERSON><PERSON> geldin, {PLAYER.NAME}. İmparatorluğun bilgeliği rehberiniz olsun."
        };

        private readonly string[] sturgiaSelamlamalari = {
            "Ha! {PLAYER.NAME}! <PERSON>zey rüzgarları seni buraya getirmiş!",
            "Ho<PERSON> geldin, savaşçı {PLAYER.NAME}! <PERSON>zey seni selamlıyor!",
            "Merhaba {PLAYER.NAME}! Buzul gibi soğuk ama kalbin sıcak!",
            "Selamlar, {PLAYER.NAME}! Kuzey ayıları kadar güçlüsün!",
            "Ha! {PLAYER.NAME}! Kar fırtınası gibi güçlü geliyorsun!"
        };

        private readonly string[] aseraiSelamlamalari = {
            "Çölün kumları üzerinde yürüyen {PLAYER.NAME}, hoş geldiniz.",
            "Selamlar, {PLAYER.NAME}. Çöl güneşi kadar parlak geliyorsunuz.",
            "Hoş geldin, {PLAYER.NAME}. Çöl rüzgarları sizi getirmiş.",
            "Merhaba, {PLAYER.NAME}. Çölün bilgeliği sizinle olsun.",
            "Selamlar, asil {PLAYER.NAME}. Çöl yıldızları size rehberlik etsin."
        };

        private readonly string[] vlandiaSelamlamalari = {
            "Şerefli {PLAYER.NAME}! Şövalyelik onuru sizinle olsun.",
            "Hoş geldiniz, {PLAYER.NAME}! Şövalyelik ruhunuz takdire şayan.",
            "Selamlar, asil {PLAYER.NAME}! Şeref ve onur sizinle olsun.",
            "Merhaba, {PLAYER.NAME}! Şövalye kalbiniz altın değerinde.",
            "Hoş geldin, {PLAYER.NAME}! Şövalyelik geleneği sizinle yaşıyor."
        };

        private readonly string[] battaniaSelamlamalari = {
            "Ormanların çocuğu {PLAYER.NAME}, hoş geldin!",
            "Selamlar, {PLAYER.NAME}! Orman ruhları sizi korusun.",
            "Hoş geldin, {PLAYER.NAME}! Ağaçlar sizin hikayenizi fısıldıyor.",
            "Merhaba, {PLAYER.NAME}! Doğanın gücü sizinle olsun.",
            "Selamlar, {PLAYER.NAME}! Ormanın bilgeliği rehberiniz olsun."
        };

        private readonly string[] khuzaitSelamlamalari = {
            "Bozkırların atı {PLAYER.NAME}! Rüzgar gibi hızlı hoş geldin!",
            "Selamlar, {PLAYER.NAME}! Bozkır kartalı gibi özgürsün!",
            "Hoş geldin, {PLAYER.NAME}! At koşusu kadar hızlı geliyorsun!",
            "Merhaba, {PLAYER.NAME}! Bozkır rüzgarları seni getirmiş!",
            "Selamlar, {PLAYER.NAME}! Gökyüzü kadar sınırsızsın!"
        };

        public void AddDialogs(CampaignGameStarter campaignStarter)
        {
            this.AddEmpireDialoglari(campaignStarter);
            this.AddSturgiaDialoglari(campaignStarter);
            this.AddAseraiDialoglari(campaignStarter);
            this.AddVlandiaDialoglari(campaignStarter);
            this.AddBattaniaDialoglari(campaignStarter);
            this.AddKhuzaitDialoglari(campaignStarter);
        }

        private void AddEmpireDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < empireSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"kultur_empire_{i}", "start", "kultur_secenekleri",
                    empireSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          CharacterObject.OneToOneConversationCharacter.Culture?.StringId == "empire" &&
                          MBRandom.RandomInt(0, empireSelamlamalari.Length) == index,
                    null);
            }

            // Empire seçenekleri
            campaignStarter.AddPlayerLine("empire_soru_1", "kultur_secenekleri", "empire_cevap_1",
                "İmparatorluğun geleceği hakkında ne düşünüyorsunuz?", 
                () => CharacterObject.OneToOneConversationCharacter?.Culture?.StringId == "empire", null);

            campaignStarter.AddDialogLine("empire_cevap_1", "empire_cevap_1", "kultur_secenekleri",
                "İmparatorluk bin yıldır ayakta. Sizin gibi kahramanlarla bin yıl daha sürecek.",
                null, () => GiveKulturOdulu(25));

            campaignStarter.AddPlayerLine("kultur_cikis", "kultur_secenekleri", "close_window",
                "Teşekkürler, hoşça kalın.", null, null);
        }

        private void AddSturgiaDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < sturgiaSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"kultur_sturgia_{i}", "start", "kultur_secenekleri",
                    sturgiaSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          CharacterObject.OneToOneConversationCharacter.Culture?.StringId == "sturgia" &&
                          MBRandom.RandomInt(0, sturgiaSelamlamalari.Length) == index,
                    null);
            }

            // Sturgia seçenekleri
            campaignStarter.AddPlayerLine("sturgia_soru_1", "kultur_secenekleri", "sturgia_cevap_1",
                "Kuzey toprakları nasıl?", 
                () => CharacterObject.OneToOneConversationCharacter?.Culture?.StringId == "sturgia", null);

            campaignStarter.AddDialogLine("sturgia_cevap_1", "sturgia_cevap_1", "kultur_secenekleri",
                "Sert ama güzel! Kuzey insanını güçlü yapar. Sen de güçlü görünüyorsun!",
                null, () => GiveKulturOdulu(20));
        }

        private void AddAseraiDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < aseraiSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"kultur_aserai_{i}", "start", "kultur_secenekleri",
                    aseraiSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null &&
                          CharacterObject.OneToOneConversationCharacter.Culture?.StringId == "aserai" &&
                          MBRandom.RandomInt(0, aseraiSelamlamalari.Length) == index,
                    null);
            }

            // Aserai seçenekleri
            campaignStarter.AddPlayerLine("aserai_soru_1", "kultur_secenekleri", "aserai_cevap_1",
                "Çöl hayatı nasıl?",
                () => CharacterObject.OneToOneConversationCharacter?.Culture?.StringId == "aserai", null);

            campaignStarter.AddDialogLine("aserai_cevap_1", "aserai_cevap_1", "kultur_secenekleri",
                "Çöl sert ama adil. Güçlüyü ödüllendirir, zayıfı cezalandırır. Siz güçlü görünüyorsunuz.",
                null, () => GiveKulturOdulu(30));
        }

        private void AddVlandiaDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < vlandiaSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"kultur_vlandia_{i}", "start", "kultur_secenekleri",
                    vlandiaSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          CharacterObject.OneToOneConversationCharacter.Culture?.StringId == "vlandia" &&
                          MBRandom.RandomInt(0, vlandiaSelamlamalari.Length) == index,
                    null);
            }

            // Vlandia seçenekleri
            campaignStarter.AddPlayerLine("vlandia_soru_1", "kultur_secenekleri", "vlandia_cevap_1",
                "Şövalyelik geleneği nasıl?", 
                () => CharacterObject.OneToOneConversationCharacter?.Culture?.StringId == "vlandia", null);

            campaignStarter.AddDialogLine("vlandia_cevap_1", "vlandia_cevap_1", "kultur_secenekleri",
                "Şövalyelik sadece savaş değil, yaşam tarzıdır. Onur, cesaret ve adalet. Siz de bu değerleri taşıyorsunuz.",
                null, () => GiveKulturOdulu(35));
        }

        private void AddBattaniaDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < battaniaSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"kultur_battania_{i}", "start", "kultur_secenekleri",
                    battaniaSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null &&
                          CharacterObject.OneToOneConversationCharacter.Culture?.StringId == "battania" &&
                          MBRandom.RandomInt(0, battaniaSelamlamalari.Length) == index,
                    null);
            }

            // Battania seçenekleri
            campaignStarter.AddPlayerLine("battania_soru_1", "kultur_secenekleri", "battania_cevap_1",
                "Orman yaşamı nasıl?",
                () => CharacterObject.OneToOneConversationCharacter?.Culture?.StringId == "battania", null);

            campaignStarter.AddDialogLine("battania_cevap_1", "battania_cevap_1", "kultur_secenekleri",
                "Orman bize her şeyi öğretir. Sabır, bilgelik, güç. Doğayla uyum içinde yaşarız.",
                null, () => GiveKulturOdulu(28));
        }

        private void AddKhuzaitDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < khuzaitSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"kultur_khuzait_{i}", "start", "kultur_secenekleri",
                    khuzaitSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          CharacterObject.OneToOneConversationCharacter.Culture?.StringId == "khuzait" &&
                          MBRandom.RandomInt(0, khuzaitSelamlamalari.Length) == index,
                    null);
            }

            // Khuzait seçenekleri
            campaignStarter.AddPlayerLine("khuzait_soru_1", "kultur_secenekleri", "khuzait_cevap_1",
                "Bozkır yaşamı nasıl?", 
                () => CharacterObject.OneToOneConversationCharacter?.Culture?.StringId == "khuzait", null);

            campaignStarter.AddDialogLine("khuzait_cevap_1", "khuzait_cevap_1", "kultur_secenekleri",
                "Bozkır özgürlüktür! Sınırsız ufuklar, hızlı atlar, güçlü rüzgarlar. Sizin gibi özgür ruhlar için ideal!",
                null, () => GiveKulturOdulu(32));
        }

        private void GiveKulturOdulu(int xp)
        {
            if (xp > 0)
            {
                Hero.MainHero.AddSkillXp(DefaultSkills.Charm, xp);
            }
        }
    }
}