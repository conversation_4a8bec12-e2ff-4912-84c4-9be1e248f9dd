using TaleWorlds.CampaignSystem;
using TaleWorlds.CampaignSystem.Conversation;
using TaleWorlds.Core;
using TaleWorlds.Library;
using TaleWorlds.CampaignSystem.Actions;
using TaleWorlds.CampaignSystem.CharacterDevelopment;

namespace GelismisDialoglar
{
    public class BeceriDiyalogSistemi
    {
        private readonly string[] yuksekSavasBecerisiBelirtileri = {
            "<PERSON>ılıç kullanma beceriniz efsanevi, {PLAYER.NAME}!",
            "Savaş alanındaki ustalığınız takdire şayan!",
            "Silah kullanma yeteneğiniz herkesi büyülüyor!",
            "Savaşçı ruhunuz gözlerinizden okunuyor!",
            "Böyle bir savaş ustasını görmek nadir!"
        };

        private readonly string[] yuksekTicaretBecerisiBelirtileri = {
            "Altın kokusunu buradan alabiliyorum, {PLAYER.NAME}!",
            "Ticaret zekânız herkesi şaşırtıyor!",
            "Para kazanma yeteneğiniz efsanevi!",
            "Altın sizin için mıknatıs gibi!",
            "Böyle bir tüccar zekâsı nadir görülür!"
        };

        private readonly string[] yuksekLiderlikBecerisiBelirtileri = {
            "Doğuştan lider olduğunuz belli, {PLAYER.NAME}!",
            "Liderlik karizmanız etkileyici!",
            "İnsanları peşinizden sürükleme yeteneğiniz muhteşem!",
            "Böyle bir lider görmek nadir!",
            "Komuta yeteneğiniz takdire şayan!"
        };

        private readonly string[] yuksekOkculukBecerisiBelirtileri = {
            "Yay çekme beceriniz efsanevi, {PLAYER.NAME}!",
            "Ok atma ustalığınız herkesi büyülüyor!",
            "Hedefi vurmada sizin gibisi yok!",
            "Okçuluk yeteneğiniz takdire şayan!",
            "Böyle bir okçu ustası nadir görülür!"
        };

        private readonly string[] yuksekTipBecerisiBelirtileri = {
            "Şifa verme yeteneğiniz takdire şayan, {PLAYER.NAME}!",
            "Tıp bilginiz herkesi hayrete düşürüyor!",
            "Şifacı elleriniz mucize yaratıyor!",
            "Böyle bir hekim görmek nadir!",
            "Şifa verme yeteneğiniz efsanevi!"
        };

        private readonly string[] cokluBeceriBecerisiBelirtileri = {
            "Çok yeteneklisiniz, {PLAYER.NAME}! Her alanda ustasınız!",
            "Böyle çok yönlü bir kahraman nadir görülür!",
            "Her beceriye sahipsiniz! Gerçek bir usta!",
            "Çok yetenekli olmanız herkesi şaşırtıyor!",
            "Böyle bir çok yönlü kahraman efsanevi!"
        };

        public void AddDialogs(CampaignGameStarter campaignStarter)
        {
            this.AddSavasBecerisiDialoglari(campaignStarter);
            this.AddTicaretBecerisiDialoglari(campaignStarter);
            this.AddLiderlikBecerisiDialoglari(campaignStarter);
            this.AddOkculukBecerisiDialoglari(campaignStarter);
            this.AddTipBecerisiDialoglari(campaignStarter);
            this.AddCokluBeceriDialoglari(campaignStarter);
        }

        private void AddSavasBecerisiDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < yuksekSavasBecerisiBelirtileri.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"beceri_savas_{i}", "start", "beceri_secenekleri",
                    yuksekSavasBecerisiBelirtileri[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsYuksekSavasBecerisi() &&
                          MBRandom.RandomInt(0, yuksekSavasBecerisiBelirtileri.Length) == index,
                    null);
            }

            // Savaş becerisi seçenekleri
            campaignStarter.AddPlayerLine("savas_soru_1", "beceri_secenekleri", "savas_cevap_1",
                "Nasıl bu kadar güçlü oldum?", 
                () => IsYuksekSavasBecerisi(), null);

            campaignStarter.AddDialogLine("savas_cevap_1", "savas_cevap_1", "beceri_secenekleri",
                "Sürekli antrenman ve cesaret! Siz gerçek bir savaşçısınız!",
                null, () => GiveBeceriOdulu(30));

            campaignStarter.AddPlayerLine("beceri_cikis", "beceri_secenekleri", "close_window",
                "Teşekkürler!", null, null);
        }

        private void AddTicaretBecerisiDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < yuksekTicaretBecerisiBelirtileri.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"beceri_ticaret_{i}", "start", "beceri_secenekleri",
                    yuksekTicaretBecerisiBelirtileri[i],
                    () => CharacterObject.OneToOneConversationCharacter != null &&
                          IsYuksekTicaretBecerisi() &&
                          MBRandom.RandomInt(0, yuksekTicaretBecerisiBelirtileri.Length) == index,
                    null);
            }

            // Ticaret becerisi seçenekleri
            campaignStarter.AddPlayerLine("ticaret_soru_1", "beceri_secenekleri", "ticaret_cevap_1",
                "Ticaret sırlarım neler?",
                () => IsYuksekTicaretBecerisi(), null);

            campaignStarter.AddDialogLine("ticaret_cevap_1", "ticaret_cevap_1", "beceri_secenekleri",
                "Sabır, zeka ve fırsat! Siz gerçek bir tüccar ustasısınız!",
                null, () => GiveBeceriOdulu(25));
        }

        private void AddLiderlikBecerisiDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < yuksekLiderlikBecerisiBelirtileri.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"beceri_liderlik_{i}", "start", "beceri_secenekleri",
                    yuksekLiderlikBecerisiBelirtileri[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsYuksekLiderlikBecerisi() &&
                          MBRandom.RandomInt(0, yuksekLiderlikBecerisiBelirtileri.Length) == index,
                    null);
            }

            // Liderlik becerisi seçenekleri
            campaignStarter.AddPlayerLine("liderlik_soru_1", "beceri_secenekleri", "liderlik_cevap_1",
                "Liderlik sırlarım neler?", 
                () => IsYuksekLiderlikBecerisi(), null);

            campaignStarter.AddDialogLine("liderlik_cevap_1", "liderlik_cevap_1", "beceri_secenekleri",
                "Adalet, cesaret ve bilgelik! Siz doğuştan lidersiniz!",
                null, () => GiveBeceriOdulu(40));
        }

        private void AddOkculukBecerisiDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < yuksekOkculukBecerisiBelirtileri.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"beceri_okculuk_{i}", "start", "beceri_secenekleri",
                    yuksekOkculukBecerisiBelirtileri[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsYuksekOkculukBecerisi() &&
                          MBRandom.RandomInt(0, yuksekOkculukBecerisiBelirtileri.Length) == index,
                    null);
            }

            // Okçuluk becerisi seçenekleri
            campaignStarter.AddPlayerLine("okculuk_soru_1", "beceri_secenekleri", "okculuk_cevap_1",
                "Okçuluk sırlarım neler?", 
                () => IsYuksekOkculukBecerisi(), null);

            campaignStarter.AddDialogLine("okculuk_cevap_1", "okculuk_cevap_1", "beceri_secenekleri",
                "Sabır, odaklanma ve sürekli pratik! Siz gerçek bir okçu ustasısınız!",
                null, () => GiveBeceriOdulu(35));
        }

        private void AddTipBecerisiDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < yuksekTipBecerisiBelirtileri.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"beceri_tip_{i}", "start", "beceri_secenekleri",
                    yuksekTipBecerisiBelirtileri[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsYuksekTipBecerisi() &&
                          MBRandom.RandomInt(0, yuksekTipBecerisiBelirtileri.Length) == index,
                    null);
            }

            // Tıp becerisi seçenekleri
            campaignStarter.AddPlayerLine("tip_soru_1", "beceri_secenekleri", "tip_cevap_1",
                "Şifa verme sırlarım neler?", 
                () => IsYuksekTipBecerisi(), null);

            campaignStarter.AddDialogLine("tip_cevap_1", "tip_cevap_1", "beceri_secenekleri",
                "Şefkat, bilgi ve tecrübe! Siz gerçek bir şifacısınız!",
                null, () => GiveBeceriOdulu(45));
        }

        private void AddCokluBeceriDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < cokluBeceriBecerisiBelirtileri.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"beceri_coklu_{i}", "start", "beceri_secenekleri",
                    cokluBeceriBecerisiBelirtileri[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsCokluBeceri() &&
                          MBRandom.RandomInt(0, cokluBeceriBecerisiBelirtileri.Length) == index,
                    null);
            }

            // Çoklu beceri seçenekleri
            campaignStarter.AddPlayerLine("coklu_soru_1", "beceri_secenekleri", "coklu_cevap_1",
                "Nasıl bu kadar çok yetenekli oldum?", 
                () => IsCokluBeceri(), null);

            campaignStarter.AddDialogLine("coklu_cevap_1", "coklu_cevap_1", "beceri_secenekleri",
                "Sürekli öğrenme ve çalışma! Siz gerçek bir ustasınız!",
                null, () => GiveBeceriOdulu(100));
        }

        // Beceri kontrol metodları
        private bool IsYuksekSavasBecerisi()
        {
            int savas = Hero.MainHero.GetSkillValue(DefaultSkills.OneHanded) + 
                       Hero.MainHero.GetSkillValue(DefaultSkills.TwoHanded) + 
                       Hero.MainHero.GetSkillValue(DefaultSkills.Polearm);
            return savas > 400;
        }

        private bool IsYuksekTicaretBecerisi()
        {
            return Hero.MainHero.GetSkillValue(DefaultSkills.Trade) > 150;
        }

        private bool IsYuksekLiderlikBecerisi()
        {
            return Hero.MainHero.GetSkillValue(DefaultSkills.Leadership) > 150;
        }

        private bool IsYuksekOkculukBecerisi()
        {
            return Hero.MainHero.GetSkillValue(DefaultSkills.Bow) > 150;
        }

        private bool IsYuksekTipBecerisi()
        {
            return Hero.MainHero.GetSkillValue(DefaultSkills.Medicine) > 150;
        }

        private bool IsCokluBeceri()
        {
            int yuksekBeceriSayisi = 0;
            
            if (Hero.MainHero.GetSkillValue(DefaultSkills.OneHanded) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.TwoHanded) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Polearm) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Bow) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Crossbow) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Throwing) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Riding) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Athletics) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Crafting) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Scouting) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Tactics) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Roguery) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Charm) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Leadership) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Trade) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Steward) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Medicine) > 120) yuksekBeceriSayisi++;
            if (Hero.MainHero.GetSkillValue(DefaultSkills.Engineering) > 120) yuksekBeceriSayisi++;

            return yuksekBeceriSayisi >= 5;
        }

        private void GiveBeceriOdulu(int xp)
        {
            if (xp > 0)
            {
                Hero.MainHero.AddSkillXp(DefaultSkills.Charm, xp);
            }
        }
    }
}