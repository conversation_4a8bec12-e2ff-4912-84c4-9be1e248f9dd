{"format": 1, "restore": {"C:\\Program Files\\Epic Games\\MountAndBlade2\\Modules\\GelismisDialoglar\\GelismisDialoglar.csproj": {}}, "projects": {"C:\\Program Files\\Epic Games\\MountAndBlade2\\Modules\\GelismisDialoglar\\GelismisDialoglar.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Program Files\\Epic Games\\MountAndBlade2\\Modules\\GelismisDialoglar\\GelismisDialoglar.csproj", "projectName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectPath": "C:\\Program Files\\Epic Games\\MountAndBlade2\\Modules\\GelismisDialoglar\\GelismisDialoglar.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Program Files\\Epic Games\\MountAndBlade2\\Modules\\GelismisDialoglar\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net472"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"targetAlias": "net472", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net472": {"targetAlias": "net472", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}