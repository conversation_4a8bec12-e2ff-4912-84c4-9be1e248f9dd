using TaleWorlds.CampaignSystem;
using TaleWorlds.CampaignSystem.Conversation;
using TaleWorlds.CampaignSystem.Party;
using TaleWorlds.Core;
using TaleWorlds.MountAndBlade;
using TaleWorlds.CampaignSystem.CharacterDevelopment;
using TaleWorlds.CampaignSystem.Actions;
using TaleWorlds.Library;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GelismisDialoglar
{
    public class DusmanDiyalogSistemi
    {
        private readonly string[] dusukUnDusmanDiyaloglari = {
            "Sen kimsin be? Adını bile duymadım!",
            "Ha! Başka bir çaylak daha. Seni kolayca ezeceğim!",
            "Kim bu böcek? Beni durdurabileceğini mi sanıyor?",
            "Adını bile bilmediğim biriyle neden savaşayım?",
            "Sen bir hiçsin! Yolumdan çekil!"
        };

        private readonly string[] ortaUnDusmanDiyaloglari = {
            "Ah, {PLAYER.NAME}... Adını duymuştum ama o kadar korkunç değilmişsin.",
            "Sen o meşhur {PLAYER.NAME} misin? Hayal kırıklığı!",
            "Ünün senden önce gelmiş ama gerçek hayal kırıklığı.",
            "Seni tanıyorum {PLAYER.NAME}, ama bu seni kurtarmayacak!",
            "Ah, {PLAYER.NAME}! Sonunda karşılaştık. Görelim bakalım ne kadar güçlüsün!"
        };

        private readonly string[] yuksekUnDusmanDiyaloglari = {
            "Lanetli {PLAYER.NAME}! Tüm Calradia senden korkuyor!",
            "Efsanevi {PLAYER.NAME}! Sonunda karşılaştık!",
            "Korkunç {PLAYER.NAME}! Ününün hakkını veriyor musun görelim!",
            "Ah, büyük {PLAYER.NAME}! Seni yenmek beni efsane yapacak!",
            "Dehşet verici {PLAYER.NAME}! Bugün senin son günün!"
        };

        private readonly string[] kisiselDusmanDiyaloglari = {
            "Sen! Beni hatırlıyor musun? İntikam zamanı!",
            "Ah, {PLAYER.NAME}! Sana olan borcumu ödeme zamanı!",
            "Sonunda seni buldum! Hesaplaşma zamanı!",
            "Sen beni yenmiştiniz ama bu sefer farklı olacak!",
            "İntikamım için çok bekledim, {PLAYER.NAME}!"
        };

        private readonly string[] korkmusDusmanDiyaloglari = {
            "Hayır... Sen... Sen gerçek {PLAYER.NAME} misin?",
            "Bu olamaz! {PLAYER.NAME} burada ne arıyor?",
            "Lütfen... Belki konuşabiliriz?",
            "Ben... Ben sadece emirleri takip ediyorum!",
            "Merhamet! Büyük {PLAYER.NAME}, merhamet!"
        };

        public void AddDialogs(CampaignGameStarter campaignStarter)
        {
            this.AddDusmanSelamlamalari(campaignStarter);
            this.AddOyuncuSecenekleri(campaignStarter);
            this.AddDusmanCevaplari(campaignStarter);
        }

        private void AddDusmanSelamlamalari(CampaignGameStarter campaignStarter)
        {
            // Düşük ün düşman diyalogları
            for (int i = 0; i < dusukUnDusmanDiyaloglari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"dusman_dusuk_un_{i}", "start", "dusman_secenekleri",
                    dusukUnDusmanDiyaloglari[i],
                    () => IsDusmanKarakter() && UnSeviyesiKontrol(0, 2) && 
                          MBRandom.RandomInt(0, dusukUnDusmanDiyaloglari.Length) == index,
                    null);
            }

            // Orta ün düşman diyalogları
            for (int i = 0; i < ortaUnDusmanDiyaloglari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"dusman_orta_un_{i}", "start", "dusman_secenekleri",
                    ortaUnDusmanDiyaloglari[i],
                    () => IsDusmanKarakter() && UnSeviyesiKontrol(3, 6) && 
                          MBRandom.RandomInt(0, ortaUnDusmanDiyaloglari.Length) == index,
                    null);
            }

            // Yüksek ün düşman diyalogları
            for (int i = 0; i < yuksekUnDusmanDiyaloglari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"dusman_yuksek_un_{i}", "start", "dusman_secenekleri",
                    yuksekUnDusmanDiyaloglari[i],
                    () => IsDusmanKarakter() && UnSeviyesiKontrol(7, 10) && 
                          MBRandom.RandomInt(0, yuksekUnDusmanDiyaloglari.Length) == index,
                    null);
            }

            // Kişisel düşman diyalogları
            for (int i = 0; i < kisiselDusmanDiyaloglari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"dusman_kisisel_{i}", "start", "dusman_secenekleri",
                    kisiselDusmanDiyaloglari[i],
                    () => IsDusmanKarakter() && IsKisiselDusman() && 
                          MBRandom.RandomInt(0, kisiselDusmanDiyaloglari.Length) == index,
                    null);
            }

            // Korkmuş düşman diyalogları
            for (int i = 0; i < korkmusDusmanDiyaloglari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"dusman_korkmus_{i}", "start", "dusman_secenekleri",
                    korkmusDusmanDiyaloglari[i],
                    () => IsDusmanKarakter() && IsKorkmusDusman() && 
                          MBRandom.RandomInt(0, korkmusDusmanDiyaloglari.Length) == index,
                    null);
            }
        }

        private void AddOyuncuSecenekleri(CampaignGameStarter campaignStarter)
        {
            // Düşük ün oyuncu seçenekleri
            campaignStarter.AddPlayerLine("oyuncu_dusuk_un_1", "dusman_secenekleri", "dusman_tepki_1",
                "Ben {PLAYER.NAME}! Adımı unutma çünkü seni yeneceğim!",
                () => UnSeviyesiKontrol(0, 2), null);

            campaignStarter.AddPlayerLine("oyuncu_dusuk_un_2", "dusman_secenekleri", "dusman_tepki_2",
                "Adımı öğreneceksin! Ben {PLAYER.NAME}!",
                () => UnSeviyesiKontrol(0, 2), null);

            // Orta ün oyuncu seçenekleri
            campaignStarter.AddPlayerLine("oyuncu_orta_un_1", "dusman_secenekleri", "dusman_tepki_3",
                "Evet, ben {PLAYER.NAME}! Ve sen benim yolumda duruyorsun!",
                () => UnSeviyesiKontrol(3, 6), null);

            campaignStarter.AddPlayerLine("oyuncu_orta_un_2", "dusman_secenekleri", "dusman_tepki_4",
                "Hayal kırıklığı mı? Görelim bakalım!",
                () => UnSeviyesiKontrol(3, 6), null);

            // Yüksek ün oyuncu seçenekleri
            campaignStarter.AddPlayerLine("oyuncu_yuksek_un_1", "dusman_secenekleri", "dusman_tepki_5",
                "Evet, ben o korkunç {PLAYER.NAME}! Teslim ol veya öl!",
                () => UnSeviyesiKontrol(7, 10), null);

            campaignStarter.AddPlayerLine("oyuncu_yuksek_un_2", "dusman_secenekleri", "dusman_tepki_6",
                "Ününüm hakkını veriyor! Hazırlan!",
                () => UnSeviyesiKontrol(7, 10), null);

            // Özel seçenekler
            campaignStarter.AddPlayerLine("oyuncu_tehdit", "dusman_secenekleri", "dusman_tepki_tehdit",
                "Seni parçalara ayıracağım!", null, () => TehditEtkisi());

            campaignStarter.AddPlayerLine("oyuncu_alay", "dusman_secenekleri", "dusman_tepki_alay",
                "Sen bir savaşçı değil, sadece şanslı bir çocuksun!", null, () => AlayEtmeEtkisi());

            campaignStarter.AddPlayerLine("oyuncu_intikam", "dusman_secenekleri", "dusman_tepki_intikam",
                "Bana yaptıklarının hesabını vereceksin!",
                () => IsKisiselDusman(), () => IntikamEtkisi());

            campaignStarter.AddPlayerLine("oyuncu_saygi", "dusman_secenekleri", "dusman_tepki_saygi",
                "Seni saygıyla selamlıyorum, düşmanım.",
                () => UnSeviyesiKontrol(5, 10), () => SaygiGosterme());

            // Savaş başlat
            campaignStarter.AddPlayerLine("oyuncu_savas", "dusman_secenekleri", "close_window",
                "Konuşma zamanı bitti! Savaş!", null, null);
        }

        private void AddDusmanCevaplari(CampaignGameStarter campaignStarter)
        {
            // Düşman tepkileri
            campaignStarter.AddDialogLine("dusman_tepki_1", "dusman_tepki_1", "dusman_secenekleri",
                "Ha! Büyük laflar! Görelim bakalım savaşta da böyle konuşabilecek misin!",
                null, null);

            campaignStarter.AddDialogLine("dusman_tepki_2", "dusman_tepki_2", "dusman_secenekleri",
                "Adını mezar taşına yazdıracağım!",
                null, null);

            campaignStarter.AddDialogLine("dusman_tepki_3", "dusman_tepki_3", "dusman_secenekleri",
                "Yolunda durmak? Ben senin yolunu kapatacağım!",
                null, null);

            campaignStarter.AddDialogLine("dusman_tepki_4", "dusman_tepki_4", "dusman_secenekleri",
                "Evet, hayal kırıklığı! Çünkü seni öldürmek çok kolay olacak!",
                null, null);

            campaignStarter.AddDialogLine("dusman_tepki_5", "dusman_tepki_5", "dusman_secenekleri",
                "Teslim olmam! Seni yenersem efsane olurum!",
                null, null);

            campaignStarter.AddDialogLine("dusman_tepki_6", "dusman_tepki_6", "dusman_secenekleri",
                "Hazırım! Görelim ününün hakkını verebilecek misin!",
                null, null);

            campaignStarter.AddDialogLine("dusman_tepki_tehdit", "dusman_tepki_tehdit", "dusman_secenekleri",
                "Tehditlerinden korkmuyorum! Gel bakalım!",
                null, null);

            campaignStarter.AddDialogLine("dusman_tepki_alay", "dusman_tepki_alay", "dusman_secenekleri",
                "Alay mı ediyorsun? Bu seni daha da pahalıya mal olacak!",
                null, null);

            campaignStarter.AddDialogLine("dusman_tepki_intikam", "dusman_tepki_intikam", "dusman_secenekleri",
                "İntikam mı? Ben de senden intikam alacağım!",
                null, null);

            campaignStarter.AddDialogLine("dusman_tepki_saygi", "dusman_tepki_saygi", "dusman_secenekleri",
                "Saygın takdire şayan... Ama bu seni kurtarmayacak!",
                null, null);
        }

        // Yardımcı metodlar
        private bool IsDusmanKarakter()
        {
            var karakter = CharacterObject.OneToOneConversationCharacter;
            if (karakter?.HeroObject == null) return false;

            return FactionManager.IsAtWarAgainstFaction(
                Hero.MainHero.MapFaction, 
                karakter.HeroObject.MapFaction);
        }

        private bool UnSeviyesiKontrol(int minUn, int maxUn)
        {
            int oyuncuUnu = (Hero.MainHero.GetSkillValue(DefaultSkills.Leadership) + 
                           Hero.MainHero.GetSkillValue(DefaultSkills.OneHanded) + 
                           Hero.MainHero.GetSkillValue(DefaultSkills.TwoHanded)) / 30;
            return oyuncuUnu >= minUn && oyuncuUnu <= maxUn;
        }

        private bool IsKisiselDusman()
        {
            var karakter = CharacterObject.OneToOneConversationCharacter;
            if (karakter?.HeroObject == null) return false;

            return CharacterRelationManager.GetHeroRelation(Hero.MainHero, karakter.HeroObject) <= -30;
        }

        private bool IsKorkmusDusman()
        {
            var karakter = CharacterObject.OneToOneConversationCharacter;
            if (karakter?.HeroObject == null) return false;

            int oyuncuGucu = Hero.MainHero.GetSkillValue(DefaultSkills.Leadership) + 
                           Hero.MainHero.GetSkillValue(DefaultSkills.OneHanded);
            int dusmanGucu = karakter.HeroObject.GetSkillValue(DefaultSkills.Leadership) + 
                           karakter.HeroObject.GetSkillValue(DefaultSkills.OneHanded);

            return oyuncuGucu > dusmanGucu * 1.5f;
        }

        // Etki metodları
        private void TehditEtkisi()
        {
            Hero.MainHero.AddSkillXp(DefaultSkills.Leadership, 25);
            if (Hero.MainHero.PartyBelongedTo != null)
            {
                Hero.MainHero.PartyBelongedTo.RecentEventsMorale += 5;
            }
            GiveGoldAction.ApplyBetweenCharacters(null, Hero.MainHero, 10, false);
        }

        private void AlayEtmeEtkisi()
        {
            Hero.MainHero.AddSkillXp(DefaultSkills.Charm, 15);
            var karakter = CharacterObject.OneToOneConversationCharacter;
            if (karakter?.HeroObject != null)
            {
                ChangeRelationAction.ApplyPlayerRelation(karakter.HeroObject, -5, true, true);
            }
            GiveGoldAction.ApplyBetweenCharacters(null, Hero.MainHero, 5, false);
        }

        private void IntikamEtkisi()
        {
            Hero.MainHero.AddSkillXp(DefaultSkills.Leadership, 35);
            if (Hero.MainHero.PartyBelongedTo != null)
            {
                Hero.MainHero.PartyBelongedTo.RecentEventsMorale += 10;
            }
            GiveGoldAction.ApplyBetweenCharacters(null, Hero.MainHero, 20, false);
        }

        private void SaygiGosterme()
        {
            Hero.MainHero.AddSkillXp(DefaultSkills.Leadership, 30);
            var karakter = CharacterObject.OneToOneConversationCharacter;
            if (karakter?.HeroObject != null)
            {
                ChangeRelationAction.ApplyPlayerRelation(karakter.HeroObject, 2, true, true);
            }
            GiveGoldAction.ApplyBetweenCharacters(null, Hero.MainHero, 15, false);
        }
    }
}