using TaleWorlds.CampaignSystem;
using TaleWorlds.CampaignSystem.Conversation;
using TaleWorlds.CampaignSystem.Party;
using TaleWorlds.Core;
using TaleWorlds.MountAndBlade;
using TaleWorlds.CampaignSystem.CharacterDevelopment;
using TaleWorlds.CampaignSystem.Actions;
using TaleWorlds.Library;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GelismisDialoglar
{
    public class GelismisDialogSistemi
    {
        // Dost faction kralları için
        private readonly string[] dostKralSelamlamalari = {
            "Ah, {PLAYER.NAME}! Krallığımın en değerli savaşçısı. Ne getirdi seni buraya?",
            "<PERSON><PERSON> geldin, {PLAYER.NAME}. Tahtımın gölgesinde rahat et ve derdini anlat.",
            "Şanlı {PLAYER.NAME}! Senin gibi cesur savaşçılar krallığımın gururudur.",
            "Merhaba {PLAYER.NAME}. Krallığımın geleceği senin gibi kahramanlara bağlı.",
            "<PERSON><PERSON> geldin, sadık {PLAYER.NAME}. Tahtımın yanında her zaman yerin var."
        };

        // Düşman faction kralları için
        private readonly string[] dusmanKralSelamlamalari = {
            "Ah, {PLAYER.NAME}... Düşman topraklarında ne arıyorsun?",
            "Sen o meşhur {PLAYER.NAME} misin? Cesaretin takdire şayan.",
            "Hmm, {PLAYER.NAME}. Savaş alanında karşılaştığımız isim.",
            "Düşman olsak da, {PLAYER.NAME}, ününü duymuştum.",
            "Ne getirdi seni buraya, {PLAYER.NAME}? Barış mı, savaş mı?"
        };

        // Nötr faction kralları için
        private readonly string[] notrKralSelamlamalari = {
            "Selamlar, {PLAYER.NAME}. Uzak diyarlardan geliyorsun.",
            "Hoş geldin, {PLAYER.NAME}. Adını duymuştum.",
            "Merhaba {PLAYER.NAME}. Ne rüzgar attı seni bu topraklara?",
            "Selamlar, yabancı {PLAYER.NAME}. Barış içinde geliyorsun umarım.",
            "Hoş geldin {PLAYER.NAME}. Diplomatik bir ziyaret mi bu?"
        };

        // Dost faction kraliçeleri için
        private readonly string[] dostKraliceSelamlamalari = {
            "Hoş geldiniz, {PLAYER.NAME}. Sarayımda sizi görmek büyük bir onur.",
            "Merhaba {PLAYER.NAME}. Zarafetiniz sarayımızı aydınlatıyor.",
            "Hoş geldin, şerefli {PLAYER.NAME}. Kraliçenin huzurunda rahat hisset.",
            "Selamlar, {PLAYER.NAME}. Sizin gibi asil ruhlar krallığımızın gururudur."
        };

        // Düşman faction kraliçeleri için
        private readonly string[] dusmanKraliceSelamlamalari = {
            "Ah, {PLAYER.NAME}... Düşman topraklarında bir kraliçeyle konuşuyorsun.",
            "Sen o ünlü {PLAYER.NAME} misin? Cesaretin etkileyici.",
            "Merhaba {PLAYER.NAME}. Savaş zamanlarında bile nezaket önemli.",
            "Düşman olsak da, {PLAYER.NAME}, saygı duyuyorum size."
        };

        // Nötr faction kraliçeleri için
        private readonly string[] notrKraliceSelamlamalari = {
            "Selamlar, {PLAYER.NAME}. Uzak diyarlardan gelen misafir.",
            "Hoş geldiniz, {PLAYER.NAME}. Diplomatik bir ziyaret mi?",
            "Merhaba {PLAYER.NAME}. Barış içinde geliyorsunuz umarım.",
            "Selamlar, yabancı {PLAYER.NAME}. Sarayımıza hoş geldiniz."
        };

        private readonly string[] soylukadınSelamlamalari = {
            "Merhaba, {PLAYER.NAME}. Sizinle tanışmak ne güzel!",
            "Hoş geldiniz, {PLAYER.NAME}. Bu güzel günde sizi görmek ne hoş!",
            "Selamlar, {PLAYER.NAME}. Zarafetiniz g��z kamaştırıyor.",
            "Merhaba {PLAYER.NAME}. Sizin gibi asil bir ruhu görmek mutluluk verici.",
            "Hoş geldin, {PLAYER.NAME}. Varlığınız ortamı güzelleştiriyor."
        };

        private readonly string[] tuccarSelamlamalari = {
            "Hoş geldin, {PLAYER.NAME}! İyi bir müşteri görmek her zaman keyifli.",
            "Merhaba {PLAYER.NAME}! Bugün hangi mallarla ilgileniyorsunuz?",
            "Selamlar, {PLAYER.NAME}! Ticaret her zaman kazançlı olsun!",
            "Hoş geldiniz, {PLAYER.NAME}! En kaliteli mallarım sizin için hazır.",
            "Merhaba {PLAYER.NAME}! Altın kokusunu buradan alabiliyorum!",
            "Hoş geldin, {PLAYER.NAME}! Bugün özel indirimlerim var."
        };

        // Soylu statüsündeki oyuncular için köylü diyalogları
        private readonly string[] soyluyaKoyluSelamlamalari = {
            "Merhaba efendim! Sizi buralarda görmek ne şeref!",
            "Hoş geldiniz, {PLAYER.NAME}! Mütevazı evimiz sizin için açık.",
            "Selamlar, {PLAYER.NAME}! Böyle büyük bir kahramanı görmek ne onur!",
            "Merhaba efendim! Sizin gibi asil birini ağırlamak büyük şeref."
        };

        // Sıradan oyuncular için köylü diyalogları
        private readonly string[] siradanKoyluSelamlamalari = {
            "Merhaba {PLAYER.NAME}! Hoş geldin köyümüze.",
            "Selamlar {PLAYER.NAME}! Uzak yerlerden geliyorsun galiba.",
            "Hoş geldin {PLAYER.NAME}! Ne rüzgar attı seni buraya?",
            "Merhaba {PLAYER.NAME}! Yolculuk nasıl geçti?"
        };

        // Dost faction askerleri için (ünvansız)
        private readonly string[] dostAskerSelamlamalari = {
            "Selamlar, {PLAYER.NAME}! Sizin gibi bir liderin yanında olmak onur!",
            "Merhaba {PLAYER.NAME}! Savaş alanında sizinle omuz omuza savaşmak isterim!",
            "Hoş geldiniz, {PLAYER.NAME}! Cesaretiniz bize ilham veriyor!",
            "Selamlar, {PLAYER.NAME}! Ordumuzun gururusunuz!"
        };

        // Dost faction askerleri için (ünvanlı)
        private readonly string[] dostUnvanliAskerSelamlamalari = {
            "Komutanım! Emirlerinizi bekliyorum!",
            "Merhaba komutanım! Savaş alanında sizinle omuz omuza savaşmak isterim!",
            "Komutanım! Sizin için ölmeye hazırım!",
            "Emrinize amadeyım, komutanım!"
        };

        // Düşman faction askerleri için
        private readonly string[] dusmanAskerSelamlamalari = {
            "Sen... {PLAYER.NAME} misin? Düşman topraklarındasın!",
            "Dur! Sen kimsin? Bu bölgede ne işin var?",
            "Ah, ünlü {PLAYER.NAME}... Savaş alanında karşılaştık demek.",
            "Dikkatli ol, {PLAYER.NAME}. Burada hoş karşılanmazsın."
        };

        // Nötr faction askerleri için
        private readonly string[] notrAskerSelamlamalari = {
            "Selamlar, yabancı. Sen {PLAYER.NAME} misin?",
            "Merhaba {PLAYER.NAME}. Bu topraklarda ne arıyorsun?",
            "Hoş geldin {PLAYER.NAME}. Barış içinde geliyorsun umarım.",
            "Selamlar {PLAYER.NAME}. Uzak diyarlardan geliyorsun."
        };

        private readonly string[] yasliSelamlamalari = {
            "Ah, {PLAYER.NAME}! Yaşlı gözlerim seni görmekten mutlu.",
            "Hoş geldin evladım. Senin yaşındayken ben de böyle cesurdum.",
            "Merhaba {PLAYER.NAME}. Tecrübelerim sana yardımcı olabilir.",
            "Selamlar, genç savaşçı. Yaşlılığın bilgeliği seninle olsun.",
            "Hoş geldin {PLAYER.NAME}. Senin gibi gençlerde umut görüyorum.",
            "Merhaba evladım. Yaşımın verdiği tecrübeyi paylaşabilirim."
        };

        private readonly string[] gencSelamlamalari = {
            "Vay be! Gerçek {PLAYER.NAME}! Sizi görmek hayal gibiydi!",
            "Merhaba {PLAYER.NAME}! Sizin gibi olmak istiyorum!",
            "Hoş geldiniz! Hikayelerinizi dinlemek isterim!",
            "Selamlar, {PLAYER.NAME}! Siz benim kahramanımsınız!",
            "Vay canına! Efsanevi {PLAYER.NAME} burada!",
            "Merhaba! Sizin maceralarınızı duymak istiyorum!"
        };

        public void AddDialogs(CampaignGameStarter campaignStarter)
        {
            this.AddKralDialoglari(campaignStarter);
            this.AddKraliceDialoglari(campaignStarter);
            this.AddSoyluKadinDialoglari(campaignStarter);
            this.AddTuccarDialoglari(campaignStarter);
            this.AddKoyluDialoglari(campaignStarter);
            this.AddAskerDialoglari(campaignStarter);
            this.AddYasliDialoglari(campaignStarter);
            this.AddGencDialoglari(campaignStarter);
        }

        private void AddKralDialoglari(CampaignGameStarter campaignStarter)
        {
            // Dost faction kralları
            for (int i = 0; i < dostKralSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"dost_kral_selam_{i}", "start", "kral_secenekleri",
                    dostKralSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null &&
                          IsKingdomLeader(CharacterObject.OneToOneConversationCharacter) &&
                          CharacterObject.OneToOneConversationCharacter.IsFemale == false &&
                          IsFriendlyFaction(CharacterObject.OneToOneConversationCharacter) &&
                          MBRandom.RandomInt(0, dostKralSelamlamalari.Length) == index,
                    null);
            }

            // Düşman faction kralları
            for (int i = 0; i < dusmanKralSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"dusman_kral_selam_{i}", "start", "dusman_kral_secenekleri",
                    dusmanKralSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null &&
                          IsKingdomLeader(CharacterObject.OneToOneConversationCharacter) &&
                          CharacterObject.OneToOneConversationCharacter.IsFemale == false &&
                          IsEnemyFaction(CharacterObject.OneToOneConversationCharacter) &&
                          MBRandom.RandomInt(0, dusmanKralSelamlamalari.Length) == index,
                    null);
            }

            // Nötr faction kralları
            for (int i = 0; i < notrKralSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"notr_kral_selam_{i}", "start", "notr_kral_secenekleri",
                    notrKralSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null &&
                          IsKingdomLeader(CharacterObject.OneToOneConversationCharacter) &&
                          CharacterObject.OneToOneConversationCharacter.IsFemale == false &&
                          IsNeutralFaction(CharacterObject.OneToOneConversationCharacter) &&
                          MBRandom.RandomInt(0, notrKralSelamlamalari.Length) == index,
                    null);
            }

            // Dost faction kral seçenekleri
            campaignStarter.AddPlayerLine("kral_soru_1", "kral_secenekleri", "kral_cevap_1",
                "Majestelerinin krallığının durumu nasıl?", null, null);

            campaignStarter.AddPlayerLine("kral_soru_2", "kral_secenekleri", "kral_cevap_2",
                "Düşmanlarımız hakkında ne düşünüyorsunuz?", null, null);

            campaignStarter.AddPlayerLine("kral_soru_3", "kral_secenekleri", "kral_cevap_3",
                "Bana özel bir görev var mı?", null, null);

            // Dost faction kral cevapları
            campaignStarter.AddDialogLine("kral_cevap_1", "kral_cevap_1", "kral_secenekleri",
                "Krallığım güçlü ve halkım müreffeh. Senin gibi sadık savaşçılar sayesinde tahtım sağlam.",
                null, () => GiveSkillReward(25));

            campaignStarter.AddDialogLine("kral_cevap_2", "kral_cevap_2", "kral_secenekleri",
                "Düşmanlarımız çoktur ama hiçbiri bizim birliğimiz kadar güçlü değil. Onları tek tek ezeceğiz.",
                null, () => GiveSkillReward(30));

            campaignStarter.AddDialogLine("kral_cevap_3", "kral_cevap_3", "kral_secenekleri",
                "Evet, sınır bölgelerimizde düşman aktivitesi var. Orayı temizlersen seni ödüllendiririm.",
                null, () => GiveSkillReward(50));

            // Düşman faction kral seçenekleri
            campaignStarter.AddPlayerLine("dusman_kral_soru_1", "dusman_kral_secenekleri", "dusman_kral_cevap_1",
                "Neden savaşıyoruz?", null, null);

            campaignStarter.AddPlayerLine("dusman_kral_soru_2", "dusman_kral_secenekleri", "dusman_kral_cevap_2",
                "Barış mümkün mü?", null, null);

            // Düşman faction kral cevapları
            campaignStarter.AddDialogLine("dusman_kral_cevap_1", "dusman_kral_cevap_1", "dusman_kral_secenekleri",
                "Savaş kaçınılmazdı. Senin halkın bizim topraklarımızı istedi.",
                null, null);

            campaignStarter.AddDialogLine("dusman_kral_cevap_2", "dusman_kral_cevap_2", "dusman_kral_secenekleri",
                "Barış... belki. Ama önce güç dengesinin değişmesi gerek.",
                null, null);

            // Nötr faction kral seçenekleri
            campaignStarter.AddPlayerLine("notr_kral_soru_1", "notr_kral_secenekleri", "notr_kral_cevap_1",
                "Krallığınız hakkında bilgi alabilir miyim?", null, null);

            campaignStarter.AddPlayerLine("notr_kral_soru_2", "notr_kral_secenekleri", "notr_kral_cevap_2",
                "Ticaret imkanları var mı?", null, null);

            // Nötr faction kral cevapları
            campaignStarter.AddDialogLine("notr_kral_cevap_1", "notr_kral_cevap_1", "notr_kral_secenekleri",
                "Krallığımız barışçıl ve ticaretle gelişiyor. Hoş geldin, yabancı.",
                null, null);

            campaignStarter.AddDialogLine("notr_kral_cevap_2", "notr_kral_cevap_2", "notr_kral_secenekleri",
                "Elbette! Tüccarlarımız her zaman yeni ortaklıklar arıyor.",
                null, null);

            // Çıkış seçenekleri
            campaignStarter.AddPlayerLine("kral_cikis", "kral_secenekleri", "close_window",
                "Şimdilik bu kadar. Hoşça kalın, Majestelerim.", null, null);

            campaignStarter.AddPlayerLine("dusman_kral_cikis", "dusman_kral_secenekleri", "close_window",
                "Anlıyorum. Hoşça kalın.", null, null);

            campaignStarter.AddPlayerLine("notr_kral_cikis", "notr_kral_secenekleri", "close_window",
                "Teşekkürler. Hoşça kalın.", null, null);
        }

        private void AddKraliceDialoglari(CampaignGameStarter campaignStarter)
        {
            // Kraliçe selamlamaları
            for (int i = 0; i < kraliceSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"kralice_selam_{i}", "start", "kralice_secenekleri",
                    kraliceSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsKingdomLeader(CharacterObject.OneToOneConversationCharacter) &&
                          CharacterObject.OneToOneConversationCharacter.IsFemale == true &&
                          MBRandom.RandomInt(0, kraliceSelamlamalari.Length) == index,
                    null);
            }

            // Kraliçe seçenekleri
            campaignStarter.AddPlayerLine("kralice_soru_1", "kralice_secenekleri", "kralice_cevap_1",
                "Sarayın güzelliği gözlerimi kamaştırıyor.", null, null);

            campaignStarter.AddPlayerLine("kralice_soru_2", "kralice_secenekleri", "kralice_cevap_2",
                "Kraliçe olmanın zorlukları nelerdir?", null, null);

            campaignStarter.AddPlayerLine("kralice_soru_3", "kralice_secenekleri", "kralice_cevap_3",
                "Halkınız için neler yapıyorsunuz?", null, null);

            // Kraliçe cevapları
            campaignStarter.AddDialogLine("kralice_cevap_1", "kralice_cevap_1", "kralice_secenekleri",
                "Ne kadar naziksiniz! Sarayımızı beğenmeniz beni çok mutlu ediyor.",
                null, () => GiveSkillReward(20));

            campaignStarter.AddDialogLine("kralice_cevap_2", "kralice_cevap_2", "kralice_secenekleri",
                "Kraliçe olmak büyük sorumluluk. Ama halkımın mutluluğu için her şeye değer.",
                null, () => GiveSkillReward(35));

            campaignStarter.AddDialogLine("kralice_cevap_3", "kralice_cevap_3", "kralice_secenekleri",
                "Yoksullara yardım ediyorum, sanatları destekliyorum ve adaleti sağlamaya çalışıyorum.",
                null, () => GiveSkillReward(30));

            // Çıkış
            campaignStarter.AddPlayerLine("kralice_cikis", "kralice_secenekleri", "close_window",
                "Vaktinizi aldığım için özür dilerim. İyi günler, Majestelerim.", null, null);
        }

        private void AddSoyluKadinDialoglari(CampaignGameStarter campaignStarter)
        {
            // Soylu kadın selamlamaları
            for (int i = 0; i < soylukadınSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"soylu_kadin_selam_{i}", "start", "soylu_kadin_secenekleri",
                    soylukadınSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          CharacterObject.OneToOneConversationCharacter.IsFemale == true &&
                          IsNoble(CharacterObject.OneToOneConversationCharacter) &&
                          !IsKingdomLeader(CharacterObject.OneToOneConversationCharacter) &&
                          MBRandom.RandomInt(0, soylukadınSelamlamalari.Length) == index,
                    null);
            }

            // Soylu kadın seçenekleri
            campaignStarter.AddPlayerLine("soylu_kadin_soru_1", "soylu_kadin_secenekleri", "soylu_kadin_cevap_1",
                "Bu güzel günde nasıl vakit geçiriyorsunuz?", null, null);

            campaignStarter.AddPlayerLine("soylu_kadin_soru_2", "soylu_kadin_secenekleri", "soylu_kadin_cevap_2",
                "Saray hayatı nasıl?", null, null);

            // Soylu kadın cevapları
            campaignStarter.AddDialogLine("soylu_kadin_cevap_1", "soylu_kadin_cevap_1", "soylu_kadin_secenekleri",
                "Bahçemde çiçeklerle vakit geçiriyorum. Doğanın güzelliği ruhumu dinlendiriyor.",
                null, () => GiveSkillReward(15));

            campaignStarter.AddDialogLine("soylu_kadin_cevap_2", "soylu_kadin_cevap_2", "soylu_kadin_secenekleri",
                "Saray hayatı güzel ama bazen özgürlüğü özlüyorum. Sizin maceralarınızı kıskanıyorum.",
                null, () => GiveSkillReward(20));

            // Çıkış
            campaignStarter.AddPlayerLine("soylu_kadin_cikis", "soylu_kadin_secenekleri", "close_window",
                "Sohbetiniz çok keyifliydi. Görüşmek üzere.", null, null);
        }

        private void AddTuccarDialoglari(CampaignGameStarter campaignStarter)
        {
            // Tüccar selamlamaları
            for (int i = 0; i < tuccarSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"tuccar_selam_{i}", "start", "tuccar_secenekleri",
                    tuccarSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsMerchant(CharacterObject.OneToOneConversationCharacter) &&
                          MBRandom.RandomInt(0, tuccarSelamlamalari.Length) == index,
                    null);
            }

            // Tüccar seçenekleri
            campaignStarter.AddPlayerLine("tuccar_soru_1", "tuccar_secenekleri", "tuccar_cevap_1",
                "Hangi mallarınız en çok satıyor?", null, null);

            campaignStarter.AddPlayerLine("tuccar_soru_2", "tuccar_secenekleri", "tuccar_cevap_2",
                "Ticaret yolları güvenli mi?", null, null);

            // Tüccar cevapları
            campaignStarter.AddDialogLine("tuccar_cevap_1", "tuccar_cevap_1", "tuccar_secenekleri",
                "Silah ve zırh her zaman rağbet görür. Savaş zamanlarında altın değerinde!",
                null, () => GiveSkillReward(12));

            campaignStarter.AddDialogLine("tuccar_cevap_2", "tuccar_cevap_2", "tuccar_secenekleri",
                "Eşkıyalar arttı son zamanlarda. Sizin gibi kahramanlar sayesinde yollar daha güvenli.",
                null, () => GiveSkillReward(18));

            // Çıkış
            campaignStarter.AddPlayerLine("tuccar_cikis", "tuccar_secenekleri", "close_window",
                "Başka zaman alışveriş yaparız. Hoşça kalın.", null, null);
        }

        private void AddKoyluDialoglari(CampaignGameStarter campaignStarter)
        {
            // Soylu statüsündeki oyuncular için köylü selamlamaları
            for (int i = 0; i < soyluyaKoyluSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"soyluya_koylu_selam_{i}", "start", "koylu_secenekleri",
                    soyluyaKoyluSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null &&
                          IsVillager(CharacterObject.OneToOneConversationCharacter) &&
                          HasNobleStatus() &&
                          MBRandom.RandomInt(0, soyluyaKoyluSelamlamalari.Length) == index,
                    null);
            }

            // Sıradan oyuncular için köylü selamlamaları
            for (int i = 0; i < siradanKoyluSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"siradan_koylu_selam_{i}", "start", "koylu_secenekleri",
                    siradanKoyluSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null &&
                          IsVillager(CharacterObject.OneToOneConversationCharacter) &&
                          IsCommoner() &&
                          MBRandom.RandomInt(0, siradanKoyluSelamlamalari.Length) == index,
                    null);
            }

            // Köylü seçenekleri
            campaignStarter.AddPlayerLine("koylu_soru_1", "koylu_secenekleri", "koylu_cevap_1",
                "Köyünüzün durumu nasıl?", null, null);

            campaignStarter.AddPlayerLine("koylu_soru_2", "koylu_secenekleri", "koylu_cevap_2",
                "Eşkıyalar rahatsız ediyor mu?", null, null);

            // Köylü cevapları (soylu statüsüne göre)
            campaignStarter.AddDialogLine("koylu_cevap_1", "koylu_cevap_1", "koylu_secenekleri",
                "Hamdolsun, mahsulümüz iyi. Sizin gibi koruyucularımız olduğu sürece endişemiz yok.",
                () => HasNobleStatus(),
                () => GiveSkillReward(8));

            campaignStarter.AddDialogLine("koylu_cevap_1_siradan", "koylu_cevap_1", "koylu_secenekleri",
                "İyi gidiyor. Sen de yolcu musun?",
                () => IsCommoner(),
                () => GiveSkillReward(5));

            campaignStarter.AddDialogLine("koylu_cevap_2", "koylu_cevap_2", "koylu_secenekleri",
                "Bazen geliyorlar ama sizin ününüz onları korkutuyor.",
                () => HasNobleStatus(),
                () => GiveSkillReward(10));

            campaignStarter.AddDialogLine("koylu_cevap_2_siradan", "koylu_cevap_2", "koylu_secenekleri",
                "Ara sıra sorun oluyor. Dikkatli ol yollarda.",
                () => IsCommoner(),
                () => GiveSkillReward(5));

            // Çıkış
            campaignStarter.AddPlayerLine("koylu_cikis", "koylu_secenekleri", "close_window",
                "İyi çalışmalar. Kendinize iyi bakın.", null, null);
        }

        private void AddAskerDialoglari(CampaignGameStarter campaignStarter)
        {
            // Dost faction askerleri (ünvansız oyuncular için)
            for (int i = 0; i < dostAskerSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"dost_asker_selam_{i}", "start", "asker_secenekleri",
                    dostAskerSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null &&
                          IsSoldier(CharacterObject.OneToOneConversationCharacter) &&
                          IsFriendlyFaction(CharacterObject.OneToOneConversationCharacter) &&
                          !HasMilitaryRank() &&
                          MBRandom.RandomInt(0, dostAskerSelamlamalari.Length) == index,
                    null);
            }

            // Dost faction askerleri (ünvanlı oyuncular için)
            for (int i = 0; i < dostUnvanliAskerSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"dost_unvanli_asker_selam_{i}", "start", "asker_secenekleri",
                    dostUnvanliAskerSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null &&
                          IsSoldier(CharacterObject.OneToOneConversationCharacter) &&
                          IsFriendlyFaction(CharacterObject.OneToOneConversationCharacter) &&
                          HasMilitaryRank() &&
                          MBRandom.RandomInt(0, dostUnvanliAskerSelamlamalari.Length) == index,
                    null);
            }

            // Düşman faction askerleri
            for (int i = 0; i < dusmanAskerSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"dusman_asker_selam_{i}", "start", "dusman_asker_secenekleri",
                    dusmanAskerSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null &&
                          IsSoldier(CharacterObject.OneToOneConversationCharacter) &&
                          IsEnemyFaction(CharacterObject.OneToOneConversationCharacter) &&
                          MBRandom.RandomInt(0, dusmanAskerSelamlamalari.Length) == index,
                    null);
            }

            // Nötr faction askerleri
            for (int i = 0; i < notrAskerSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"notr_asker_selam_{i}", "start", "notr_asker_secenekleri",
                    notrAskerSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null &&
                          IsSoldier(CharacterObject.OneToOneConversationCharacter) &&
                          IsNeutralFaction(CharacterObject.OneToOneConversationCharacter) &&
                          MBRandom.RandomInt(0, notrAskerSelamlamalari.Length) == index,
                    null);
            }

            // Dost faction asker seçenekleri
            campaignStarter.AddPlayerLine("asker_soru_1", "asker_secenekleri", "asker_cevap_1",
                "Moral nasıl asker?", null, null);

            campaignStarter.AddPlayerLine("asker_soru_2", "asker_secenekleri", "asker_cevap_2",
                "Savaşa hazır mısın?", null, null);

            // Dost faction asker cevapları
            campaignStarter.AddDialogLine("asker_cevap_1", "asker_cevap_1", "asker_secenekleri",
                "Moralimiz yüksek! Sizin liderliğinizde her savaşı kazanacağız!",
                null, () => Hero.MainHero.AddSkillXp(DefaultSkills.Leadership, 15));

            campaignStarter.AddDialogLine("asker_cevap_2", "asker_cevap_2", "asker_secenekleri",
                "Her zaman hazırım! Düşmanlar gelsin, onlara gücümüzü gösterelim!",
                null, () => Hero.MainHero.AddSkillXp(DefaultSkills.Leadership, 20));

            // Düşman faction asker seçenekleri
            campaignStarter.AddPlayerLine("dusman_asker_soru_1", "dusman_asker_secenekleri", "dusman_asker_cevap_1",
                "Neden savaşıyoruz?", null, null);

            campaignStarter.AddPlayerLine("dusman_asker_soru_2", "dusman_asker_secenekleri", "dusman_asker_cevap_2",
                "Barış mümkün mü?", null, null);

            // Düşman faction asker cevapları
            campaignStarter.AddDialogLine("dusman_asker_cevap_1", "dusman_asker_cevap_1", "dusman_asker_secenekleri",
                "Emirler böyle. Savaşmak zorundayız.",
                null, null);

            campaignStarter.AddDialogLine("dusman_asker_cevap_2", "dusman_asker_cevap_2", "dusman_asker_secenekleri",
                "Belki... ama bu benim kararım değil.",
                null, null);

            // Nötr faction asker seçenekleri
            campaignStarter.AddPlayerLine("notr_asker_soru_1", "notr_asker_secenekleri", "notr_asker_cevap_1",
                "Bu bölgede durum nasıl?", null, null);

            // Nötr faction asker cevapları
            campaignStarter.AddDialogLine("notr_asker_cevap_1", "notr_asker_cevap_1", "notr_asker_secenekleri",
                "Sakin. Büyük sorunlar yok şimdilik.",
                null, null);

            // Çıkış seçenekleri
            campaignStarter.AddPlayerLine("asker_cikis", "asker_secenekleri", "close_window",
                "Görevine devam et asker.", null, null);

            campaignStarter.AddPlayerLine("dusman_asker_cikis", "dusman_asker_secenekleri", "close_window",
                "Anlıyorum. Hoşça kal.", null, null);

            campaignStarter.AddPlayerLine("notr_asker_cikis", "notr_asker_secenekleri", "close_window",
                "Teşekkürler. Hoşça kal.", null, null);
        }

        private void AddYasliDialoglari(CampaignGameStarter campaignStarter)
        {
            // Yaşlı selamlamaları
            for (int i = 0; i < yasliSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"yasli_selam_{i}", "start", "yasli_secenekleri",
                    yasliSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          CharacterObject.OneToOneConversationCharacter.Age > 50 &&
                          MBRandom.RandomInt(0, yasliSelamlamalari.Length) == index,
                    null);
            }

            // Yaşlı seçenekleri
            campaignStarter.AddPlayerLine("yasli_soru_1", "yasli_secenekleri", "yasli_cevap_1",
                "Bana tavsiyeniz var mı?", null, null);

            campaignStarter.AddPlayerLine("yasli_soru_2", "yasli_secenekleri", "yasli_cevap_2",
                "Eski günlerden hikayeler anlatır mısınız?", null, null);

            // Yaşlı cevapları
            campaignStarter.AddDialogLine("yasli_cevap_1", "yasli_cevap_1", "yasli_secenekleri",
                "Sabırlı ol evladım. Acele etme, her şeyin zamanı var. Bilgelik yaşla gelir.",
                null, () => GiveSkillReward(25));

            campaignStarter.AddDialogLine("yasli_cevap_2", "yasli_cevap_2", "yasli_secenekleri",
                "Ah, eski günler... O zamanlar da böyle kahramanlar vardı. Sen onları hatırlatıyorsun bana.",
                null, () => GiveSkillReward(30));

            // Çıkış
            campaignStarter.AddPlayerLine("yasli_cikis", "yasli_secenekleri", "close_window",
                "Teşekkürler, bilgeleriniz için.", null, null);
        }

        private void AddGencDialoglari(CampaignGameStarter campaignStarter)
        {
            // Genç selamlamaları
            for (int i = 0; i < gencSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"genc_selam_{i}", "start", "genc_secenekleri",
                    gencSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          CharacterObject.OneToOneConversationCharacter.Age < 25 &&
                          MBRandom.RandomInt(0, gencSelamlamalari.Length) == index,
                    null);
            }

            // Genç seçenekleri
            campaignStarter.AddPlayerLine("genc_soru_1", "genc_secenekleri", "genc_cevap_1",
                "Nasıl güçlü olabilirim?", null, null);

            campaignStarter.AddPlayerLine("genc_soru_2", "genc_secenekleri", "genc_cevap_2",
                "Macera hikayelerinizi anlatır mısınız?", null, null);

            // Genç cevapları
            campaignStarter.AddDialogLine("genc_cevap_1", "genc_cevap_1", "genc_secenekleri",
                "Çok çalış, asla pes etme! Her gün antrenman yap ve adaletten ayrılma!",
                null, () => GiveSkillReward(15));

            campaignStarter.AddDialogLine("genc_cevap_2", "genc_cevap_2", "genc_secenekleri",
                "Bir gün sana tüm maceralarımı anlatırım. Ama şimdi sen kendi hikayeni yazmaya odaklan!",
                null, () => GiveSkillReward(20));

            // Çıkış
            campaignStarter.AddPlayerLine("genc_cikis", "genc_secenekleri", "close_window",
                "Teşekkürler! Sizin gibi olmaya çalışacağım!", null, null);
        }

        // Yardımcı metodlar
        private bool IsKingdomLeader(CharacterObject character)
        {
            return character?.HeroObject?.IsFactionLeader == true;
        }

        private bool IsNoble(CharacterObject character)
        {
            return character?.HeroObject?.IsLord == true;
        }

        private bool IsMerchant(CharacterObject character)
        {
            return character?.Occupation == Occupation.Merchant;
        }

        private bool IsVillager(CharacterObject character)
        {
            return character?.Occupation == Occupation.Villager;
        }

        private bool IsSoldier(CharacterObject character)
        {
            return character?.Occupation == Occupation.Soldier;
        }

        // Faction ilişkisi kontrol metodları
        private bool IsFriendlyFaction(CharacterObject character)
        {
            if (character?.HeroObject == null) return false;

            var playerFaction = Hero.MainHero.MapFaction;
            var characterFaction = character.HeroObject.MapFaction;

            // Aynı faction
            if (playerFaction == characterFaction) return true;

            // Müttefik factionlar
            if (playerFaction != null && characterFaction != null)
            {
                return FactionManager.IsAlliedWithFaction(playerFaction, characterFaction);
            }

            return false;
        }

        private bool IsEnemyFaction(CharacterObject character)
        {
            if (character?.HeroObject == null) return false;

            var playerFaction = Hero.MainHero.MapFaction;
            var characterFaction = character.HeroObject.MapFaction;

            if (playerFaction != null && characterFaction != null)
            {
                return FactionManager.IsAtWarAgainstFaction(playerFaction, characterFaction);
            }

            return false;
        }

        private bool IsNeutralFaction(CharacterObject character)
        {
            return !IsFriendlyFaction(character) && !IsEnemyFaction(character);
        }

        // Gerçekçi ünvan kontrolü
        private bool HasMilitaryRank()
        {
            // Sadece gerçek askeri ünvanlar
            return Hero.MainHero.IsFactionLeader ||
                   Hero.MainHero.IsKingdomLeader ||
                   (Hero.MainHero.Clan?.Kingdom != null && Hero.MainHero.Clan.Tier >= 3) ||
                   Hero.MainHero.GetSkillValue(DefaultSkills.Leadership) >= 150;
        }

        private bool HasNobleStatus()
        {
            // Soylu statüsü kontrolü
            return Hero.MainHero.IsLord ||
                   Hero.MainHero.Clan?.Tier >= 2 ||
                   Hero.MainHero.Gold >= 50000;
        }

        private bool IsCommoner()
        {
            // Sıradan halk
            return !HasNobleStatus() && !HasMilitaryRank();
        }

        private void GiveSkillReward(int xp)
        {
            if (xp > 0)
            {
                Hero.MainHero.AddSkillXp(DefaultSkills.Charm, xp);
            }
        }
    }
}