using TaleWorlds.CampaignSystem;
using TaleWorlds.CampaignSystem.Conversation;
using TaleWorlds.CampaignSystem.Party;
using TaleWorlds.Core;
using TaleWorlds.MountAndBlade;
using TaleWorlds.CampaignSystem.CharacterDevelopment;
using TaleWorlds.CampaignSystem.Actions;
using TaleWorlds.Library;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GelismisDialoglar
{
    public class GelismisDialogSistemi
    {
        private readonly string[] kralSelamlamalari = {
            "Ah, {PLAYER.NAME}! Krallığımın en değerli savaşçısı. Ne getirdi seni buraya?",
            "Ho<PERSON> geldin, {PLAYER.NAME}. Tahtımın gölgesinde rahat et ve derdini anlat.",
            "Şanlı {PLAYER.NAME}! Senin gibi cesur savaşçılar krallığımın gururudur.",
            "Merhaba {PLAYER.NAME}. Krallığımın geleceği senin gibi kahramanlara bağlı.",
            "<PERSON><PERSON> geldin, sadık {PLAYER.NAME}. <PERSON>htım<PERSON><PERSON> yanında her zaman yerin var.",
            "Ah, ünlü {PLAYER.NAME}! Adın tüm krallıkta yankılanıyor.",
            "Selamlar, {PLAYER.NAME}. Krallığımın en güvenilir destekçisi.",
            "Merhaba {PLAYER.NAME}. Senin varlığın sarayımı onurlandırıyor."
        };

        private readonly string[] kraliceSelamlamalari = {
            "Hoş geldiniz, {PLAYER.NAME}. Sarayımda sizi görmek büyük bir onur.",
            "Merhaba {PLAYER.NAME}. Zarafetiniz sarayımızı aydınlatıyor.",
            "Hoş geldin, şerefli {PLAYER.NAME}. Kraliçenin huzurunda rahat hisset.",
            "Selamlar, {PLAYER.NAME}. Sizin gibi asil ruhlar krallığımızın gururudur.",
            "Merhaba {PLAYER.NAME}. Nezaketiniz ve cesaretiniz takdire şayan.",
            "Hoş geldiniz, değerli {PLAYER.NAME}. Sarayımızın kapıları size her zaman açık."
        };

        private readonly string[] soylukadınSelamlamalari = {
            "Merhaba, {PLAYER.NAME}. Sizinle tanışmak ne güzel!",
            "Hoş geldiniz, {PLAYER.NAME}. Bu güzel günde sizi görmek ne hoş!",
            "Selamlar, {PLAYER.NAME}. Zarafetiniz g��z kamaştırıyor.",
            "Merhaba {PLAYER.NAME}. Sizin gibi asil bir ruhu görmek mutluluk verici.",
            "Hoş geldin, {PLAYER.NAME}. Varlığınız ortamı güzelleştiriyor."
        };

        private readonly string[] tuccarSelamlamalari = {
            "Hoş geldin, {PLAYER.NAME}! İyi bir müşteri görmek her zaman keyifli.",
            "Merhaba {PLAYER.NAME}! Bugün hangi mallarla ilgileniyorsunuz?",
            "Selamlar, {PLAYER.NAME}! Ticaret her zaman kazançlı olsun!",
            "Hoş geldiniz, {PLAYER.NAME}! En kaliteli mallarım sizin için hazır.",
            "Merhaba {PLAYER.NAME}! Altın kokusunu buradan alabiliyorum!",
            "Hoş geldin, {PLAYER.NAME}! Bugün özel indirimlerim var."
        };

        private readonly string[] koyluSelamlamalari = {
            "Merhaba efendim! Sizi buralarda görmek ne şeref!",
            "Hoş geldiniz, {PLAYER.NAME}! Mütevazı evimiz sizin için açık.",
            "Selamlar, {PLAYER.NAME}! Böyle büyük bir kahramanı görmek ne onur!",
            "Merhaba efendim! Sizin gibi asil birini ağırlamak büyük şeref.",
            "Hoş geldin, {PLAYER.NAME}! Fakir kulunuzun evi sizin eviniz.",
            "Selamlar, {PLAYER.NAME}! Adınız köyümüzde efsane oldu!"
        };

        private readonly string[] askerSelamlamalari = {
            "Komutanım! Emirlerinizi bekliyorum!",
            "Selamlar, {PLAYER.NAME}! Sizin gibi bir liderin yanında olmak onur!",
            "Merhaba komutanım! Savaş alanında sizinle omuz omuza savaşmak isterim!",
            "Hoş geldiniz, {PLAYER.NAME}! Cesaretiniz bize ilham veriyor!",
            "Selamlar, {PLAYER.NAME}! Ordumuzun gururusunuz!",
            "Komutanım! Sizin için ölmeye hazırım!"
        };

        private readonly string[] yasliSelamlamalari = {
            "Ah, {PLAYER.NAME}! Yaşlı gözlerim seni görmekten mutlu.",
            "Hoş geldin evladım. Senin yaşındayken ben de böyle cesurdum.",
            "Merhaba {PLAYER.NAME}. Tecrübelerim sana yardımcı olabilir.",
            "Selamlar, genç savaşçı. Yaşlılığın bilgeliği seninle olsun.",
            "Hoş geldin {PLAYER.NAME}. Senin gibi gençlerde umut görüyorum.",
            "Merhaba evladım. Yaşımın verdiği tecrübeyi paylaşabilirim."
        };

        private readonly string[] gencSelamlamalari = {
            "Vay be! Gerçek {PLAYER.NAME}! Sizi görmek hayal gibiydi!",
            "Merhaba {PLAYER.NAME}! Sizin gibi olmak istiyorum!",
            "Hoş geldiniz! Hikayelerinizi dinlemek isterim!",
            "Selamlar, {PLAYER.NAME}! Siz benim kahramanımsınız!",
            "Vay canına! Efsanevi {PLAYER.NAME} burada!",
            "Merhaba! Sizin maceralarınızı duymak istiyorum!"
        };

        public void AddDialogs(CampaignGameStarter campaignStarter)
        {
            this.AddKralDialoglari(campaignStarter);
            this.AddKraliceDialoglari(campaignStarter);
            this.AddSoyluKadinDialoglari(campaignStarter);
            this.AddTuccarDialoglari(campaignStarter);
            this.AddKoyluDialoglari(campaignStarter);
            this.AddAskerDialoglari(campaignStarter);
            this.AddYasliDialoglari(campaignStarter);
            this.AddGencDialoglari(campaignStarter);
        }

        private void AddKralDialoglari(CampaignGameStarter campaignStarter)
        {
            // Kral selamlamaları
            for (int i = 0; i < kralSelamlamalari.Length; i++)
            {
                int index = i; // Closure için
                campaignStarter.AddDialogLine($"kral_selam_{i}", "start", "kral_secenekleri",
                    kralSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsKingdomLeader(CharacterObject.OneToOneConversationCharacter) &&
                          CharacterObject.OneToOneConversationCharacter.IsFemale == false &&
                          MBRandom.RandomInt(0, kralSelamlamalari.Length) == index,
                    null);
            }

            // Kral seçenekleri
            campaignStarter.AddPlayerLine("kral_soru_1", "kral_secenekleri", "kral_cevap_1",
                "Majestelerinin krallığının durumu nasıl?", null, null);

            campaignStarter.AddPlayerLine("kral_soru_2", "kral_secenekleri", "kral_cevap_2",
                "Düşmanlarımız hakkında ne düşünüyorsunuz?", null, null);

            campaignStarter.AddPlayerLine("kral_soru_3", "kral_secenekleri", "kral_cevap_3",
                "Bana özel bir görev var mı?", null, null);

            campaignStarter.AddPlayerLine("kral_soru_4", "kral_secenekleri", "kral_cevap_4",
                "Krallığınızın geleceği hakkında ne düşünüyorsunuz?", null, null);

            // Kral cevapları
            campaignStarter.AddDialogLine("kral_cevap_1", "kral_cevap_1", "kral_secenekleri",
                "Krallığım güçlü ve halkım müreffeh. Senin gibi sadık savaşçılar sayesinde tahtım sağlam.",
                null, () => GiveReward(10, 25));

            campaignStarter.AddDialogLine("kral_cevap_2", "kral_cevap_2", "kral_secenekleri",
                "Düşmanlarımız çoktur ama hiçbiri bizim birliğimiz kadar güçlü değil. Onları tek tek ezeceğiz.",
                null, () => GiveReward(15, 30));

            campaignStarter.AddDialogLine("kral_cevap_3", "kral_cevap_3", "kral_secenekleri",
                "Evet, sınır bölgelerimizde düşman aktivitesi var. Orayı temizlersen seni ödüllendiririm.",
                null, () => GiveReward(20, 50));

            campaignStarter.AddDialogLine("kral_cevap_4", "kral_cevap_4", "kral_secenekleri",
                "Gelecek parlak görünüyor. Senin gibi kahramanlarla krallığım bin yıl daha sürecek.",
                null, () => GiveReward(25, 40));

            // Çıkış
            campaignStarter.AddPlayerLine("kral_cikis", "kral_secenekleri", "close_window",
                "Şimdilik bu kadar. Hoşça kalın, Majestelerim.", null, null);
        }

        private void AddKraliceDialoglari(CampaignGameStarter campaignStarter)
        {
            // Kraliçe selamlamaları
            for (int i = 0; i < kraliceSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"kralice_selam_{i}", "start", "kralice_secenekleri",
                    kraliceSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsKingdomLeader(CharacterObject.OneToOneConversationCharacter) &&
                          CharacterObject.OneToOneConversationCharacter.IsFemale == true &&
                          MBRandom.RandomInt(0, kraliceSelamlamalari.Length) == index,
                    null);
            }

            // Kraliçe seçenekleri
            campaignStarter.AddPlayerLine("kralice_soru_1", "kralice_secenekleri", "kralice_cevap_1",
                "Sarayın güzelliği gözlerimi kamaştırıyor.", null, null);

            campaignStarter.AddPlayerLine("kralice_soru_2", "kralice_secenekleri", "kralice_cevap_2",
                "Kraliçe olmanın zorlukları nelerdir?", null, null);

            campaignStarter.AddPlayerLine("kralice_soru_3", "kralice_secenekleri", "kralice_cevap_3",
                "Halkınız için neler yapıyorsunuz?", null, null);

            // Kraliçe cevapları
            campaignStarter.AddDialogLine("kralice_cevap_1", "kralice_cevap_1", "kralice_secenekleri",
                "Ne kadar naziksiniz! Sarayımızı beğenmeniz beni çok mutlu ediyor.",
                null, () => GiveReward(15, 20));

            campaignStarter.AddDialogLine("kralice_cevap_2", "kralice_cevap_2", "kralice_secenekleri",
                "Kraliçe olmak büyük sorumluluk. Ama halkımın mutluluğu için her şeye değer.",
                null, () => GiveReward(20, 35));

            campaignStarter.AddDialogLine("kralice_cevap_3", "kralice_cevap_3", "kralice_secenekleri",
                "Yoksullara yardım ediyorum, sanatları destekliyorum ve adaleti sağlamaya çalışıyorum.",
                null, () => GiveReward(25, 30));

            // Çıkış
            campaignStarter.AddPlayerLine("kralice_cikis", "kralice_secenekleri", "close_window",
                "Vaktinizi aldığım için özür dilerim. İyi günler, Majestelerim.", null, null);
        }

        private void AddSoyluKadinDialoglari(CampaignGameStarter campaignStarter)
        {
            // Soylu kadın selamlamaları
            for (int i = 0; i < soylukadınSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"soylu_kadin_selam_{i}", "start", "soylu_kadin_secenekleri",
                    soylukadınSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          CharacterObject.OneToOneConversationCharacter.IsFemale == true &&
                          IsNoble(CharacterObject.OneToOneConversationCharacter) &&
                          !IsKingdomLeader(CharacterObject.OneToOneConversationCharacter) &&
                          MBRandom.RandomInt(0, soylukadınSelamlamalari.Length) == index,
                    null);
            }

            // Soylu kadın seçenekleri
            campaignStarter.AddPlayerLine("soylu_kadin_soru_1", "soylu_kadin_secenekleri", "soylu_kadin_cevap_1",
                "Bu güzel günde nasıl vakit geçiriyorsunuz?", null, null);

            campaignStarter.AddPlayerLine("soylu_kadin_soru_2", "soylu_kadin_secenekleri", "soylu_kadin_cevap_2",
                "Saray hayatı nasıl?", null, null);

            // Soylu kadın cevapları
            campaignStarter.AddDialogLine("soylu_kadin_cevap_1", "soylu_kadin_cevap_1", "soylu_kadin_secenekleri",
                "Bahçemde çiçeklerle vakit geçiriyorum. Doğanın güzelliği ruhumu dinlendiriyor.",
                null, () => GiveReward(5, 15));

            campaignStarter.AddDialogLine("soylu_kadin_cevap_2", "soylu_kadin_cevap_2", "soylu_kadin_secenekleri",
                "Saray hayatı güzel ama bazen özgürlüğü özlüyorum. Sizin maceralarınızı kıskanıyorum.",
                null, () => GiveReward(10, 20));

            // Çıkış
            campaignStarter.AddPlayerLine("soylu_kadin_cikis", "soylu_kadin_secenekleri", "close_window",
                "Sohbetiniz çok keyifliydi. Görüşmek üzere.", null, null);
        }

        private void AddTuccarDialoglari(CampaignGameStarter campaignStarter)
        {
            // Tüccar selamlamaları
            for (int i = 0; i < tuccarSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"tuccar_selam_{i}", "start", "tuccar_secenekleri",
                    tuccarSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsMerchant(CharacterObject.OneToOneConversationCharacter) &&
                          MBRandom.RandomInt(0, tuccarSelamlamalari.Length) == index,
                    null);
            }

            // Tüccar seçenekleri
            campaignStarter.AddPlayerLine("tuccar_soru_1", "tuccar_secenekleri", "tuccar_cevap_1",
                "Hangi mallarınız en çok satıyor?", null, null);

            campaignStarter.AddPlayerLine("tuccar_soru_2", "tuccar_secenekleri", "tuccar_cevap_2",
                "Ticaret yolları güvenli mi?", null, null);

            // Tüccar cevapları
            campaignStarter.AddDialogLine("tuccar_cevap_1", "tuccar_cevap_1", "tuccar_secenekleri",
                "Silah ve zırh her zaman rağbet görür. Savaş zamanlarında altın değerinde!",
                null, () => GiveReward(8, 12));

            campaignStarter.AddDialogLine("tuccar_cevap_2", "tuccar_cevap_2", "tuccar_secenekleri",
                "Eşkıyalar arttı son zamanlarda. Sizin gibi kahramanlar sayesinde yollar daha güvenli.",
                null, () => GiveReward(12, 18));

            // Çıkış
            campaignStarter.AddPlayerLine("tuccar_cikis", "tuccar_secenekleri", "close_window",
                "Başka zaman alışveriş yaparız. Hoşça kalın.", null, null);
        }

        private void AddKoyluDialoglari(CampaignGameStarter campaignStarter)
        {
            // Köylü selamlamaları
            for (int i = 0; i < koyluSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"koylu_selam_{i}", "start", "koylu_secenekleri",
                    koyluSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsVillager(CharacterObject.OneToOneConversationCharacter) &&
                          MBRandom.RandomInt(0, koyluSelamlamalari.Length) == index,
                    null);
            }

            // Köylü seçenekleri
            campaignStarter.AddPlayerLine("koylu_soru_1", "koylu_secenekleri", "koylu_cevap_1",
                "Köyünüzün durumu nasıl?", null, null);

            campaignStarter.AddPlayerLine("koylu_soru_2", "koylu_secenekleri", "koylu_cevap_2",
                "Eşkıyalar rahatsız ediyor mu?", null, null);

            // Köylü cevapları
            campaignStarter.AddDialogLine("koylu_cevap_1", "koylu_cevap_1", "koylu_secenekleri",
                "Hamdolsun efendim, mahsulümüz iyi. Sizin gibi koruyucularımız olduğu sürece endişemiz yok.",
                null, () => GiveReward(3, 8));

            campaignStarter.AddDialogLine("koylu_cevap_2", "koylu_cevap_2", "koylu_secenekleri",
                "Bazen geliyorlar efendim ama sizin ününüz onları korkutuyor. Adınız bile onları kaçırıyor!",
                null, () => GiveReward(5, 10));

            // Çıkış
            campaignStarter.AddPlayerLine("koylu_cikis", "koylu_secenekleri", "close_window",
                "İyi çalışmalar. Kendinize iyi bakın.", null, null);
        }

        private void AddAskerDialoglari(CampaignGameStarter campaignStarter)
        {
            // Asker selamlamaları
            for (int i = 0; i < askerSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"asker_selam_{i}", "start", "asker_secenekleri",
                    askerSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          IsSoldier(CharacterObject.OneToOneConversationCharacter) &&
                          MBRandom.RandomInt(0, askerSelamlamalari.Length) == index,
                    null);
            }

            // Asker seçenekleri
            campaignStarter.AddPlayerLine("asker_soru_1", "asker_secenekleri", "asker_cevap_1",
                "Moral nasıl asker?", null, null);

            campaignStarter.AddPlayerLine("asker_soru_2", "asker_secenekleri", "asker_cevap_2",
                "Savaşa hazır mısın?", null, null);

            // Asker cevapları
            campaignStarter.AddDialogLine("asker_cevap_1", "asker_cevap_1", "asker_secenekleri",
                "Moralimiz yüksek komutanım! Sizin liderliğinizde her savaşı kazanacağız!",
                null, () => GiveReward(5, 15));

            campaignStarter.AddDialogLine("asker_cevap_2", "asker_cevap_2", "asker_secenekleri",
                "Her zaman hazırım komutanım! Düşmanlar gelsin, onlara gücümüzü gösterelim!",
                null, () => GiveReward(8, 20));

            // Çıkış
            campaignStarter.AddPlayerLine("asker_cikis", "asker_secenekleri", "close_window",
                "Görevine devam et asker.", null, null);
        }

        private void AddYasliDialoglari(CampaignGameStarter campaignStarter)
        {
            // Yaşlı selamlamaları
            for (int i = 0; i < yasliSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"yasli_selam_{i}", "start", "yasli_secenekleri",
                    yasliSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          CharacterObject.OneToOneConversationCharacter.Age > 50 &&
                          MBRandom.RandomInt(0, yasliSelamlamalari.Length) == index,
                    null);
            }

            // Yaşlı seçenekleri
            campaignStarter.AddPlayerLine("yasli_soru_1", "yasli_secenekleri", "yasli_cevap_1",
                "Bana tavsiyeniz var mı?", null, null);

            campaignStarter.AddPlayerLine("yasli_soru_2", "yasli_secenekleri", "yasli_cevap_2",
                "Eski günlerden hikayeler anlatır mısınız?", null, null);

            // Yaşlı cevapları
            campaignStarter.AddDialogLine("yasli_cevap_1", "yasli_cevap_1", "yasli_secenekleri",
                "Sabırlı ol evladım. Acele etme, her şeyin zamanı var. Bilgelik yaşla gelir.",
                null, () => GiveReward(10, 25));

            campaignStarter.AddDialogLine("yasli_cevap_2", "yasli_cevap_2", "yasli_secenekleri",
                "Ah, eski günler... O zamanlar da böyle kahramanlar vardı. Sen onları hatırlatıyorsun bana.",
                null, () => GiveReward(15, 30));

            // Çıkış
            campaignStarter.AddPlayerLine("yasli_cikis", "yasli_secenekleri", "close_window",
                "Teşekkürler, bilgeleriniz için.", null, null);
        }

        private void AddGencDialoglari(CampaignGameStarter campaignStarter)
        {
            // Genç selamlamaları
            for (int i = 0; i < gencSelamlamalari.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"genc_selam_{i}", "start", "genc_secenekleri",
                    gencSelamlamalari[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          CharacterObject.OneToOneConversationCharacter.Age < 25 &&
                          MBRandom.RandomInt(0, gencSelamlamalari.Length) == index,
                    null);
            }

            // Genç seçenekleri
            campaignStarter.AddPlayerLine("genc_soru_1", "genc_secenekleri", "genc_cevap_1",
                "Nasıl güçlü olabilirim?", null, null);

            campaignStarter.AddPlayerLine("genc_soru_2", "genc_secenekleri", "genc_cevap_2",
                "Macera hikayelerinizi anlatır mısınız?", null, null);

            // Genç cevapları
            campaignStarter.AddDialogLine("genc_cevap_1", "genc_cevap_1", "genc_secenekleri",
                "Çok çalış, asla pes etme! Her gün antrenman yap ve adaletten ayrılma!",
                null, () => GiveReward(5, 15));

            campaignStarter.AddDialogLine("genc_cevap_2", "genc_cevap_2", "genc_secenekleri",
                "Bir gün sana tüm maceralarımı anlatırım. Ama şimdi sen kendi hikayeni yazmaya odaklan!",
                null, () => GiveReward(8, 20));

            // Çıkış
            campaignStarter.AddPlayerLine("genc_cikis", "genc_secenekleri", "close_window",
                "Teşekkürler! Sizin gibi olmaya çalışacağım!", null, null);
        }

        // Yardımcı metodlar
        private bool IsKingdomLeader(CharacterObject character)
        {
            return character?.HeroObject?.IsFactionLeader == true;
        }

        private bool IsNoble(CharacterObject character)
        {
            return character?.HeroObject?.IsLord == true;
        }

        private bool IsMerchant(CharacterObject character)
        {
            return character?.Occupation == Occupation.Merchant;
        }

        private bool IsVillager(CharacterObject character)
        {
            return character?.Occupation == Occupation.Villager;
        }

        private bool IsSoldier(CharacterObject character)
        {
            return character?.Occupation == Occupation.Soldier;
        }

        private void GiveReward(int gold, int xp)
        {
            if (gold > 0)
            {
                GiveGoldAction.ApplyBetweenCharacters(null, Hero.MainHero, gold, false);
            }
            if (xp > 0)
            {
                Hero.MainHero.AddSkillXp(DefaultSkills.Charm, xp);
            }
        }
    }
}