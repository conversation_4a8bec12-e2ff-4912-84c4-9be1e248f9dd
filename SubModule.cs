using TaleWorlds.CampaignSystem;
using TaleWorlds.CampaignSystem.Conversation;
using TaleWorlds.CampaignSystem.Party;
using TaleWorlds.Core;
using TaleWorlds.MountAndBlade;
using TaleWorlds.CampaignSystem.CharacterDevelopment;
using TaleWorlds.CampaignSystem.Actions;
using TaleWorlds.Library;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GelismisDialoglar
{
    public class SubModule : MBSubModuleBase
    {
        private GelismisDialogSistemi _dialogSistemi;
        private DusmanDiyalogSistemi _dusmanSistemi;
        private KulturDiyalogSistemi _kulturSistemi;
        private BeceriDiyalogSistemi _beceriSistemi;
        private BolgeDiyalogSistemi _bolgeSistemi;
        private RastgeleOlayDiyalogSistemi _rastgeleOlaySistemi;

        protected override void OnSubModuleLoad()
        {
            base.OnSubModuleLoad();
            InformationManager.DisplayMessage(new InformationMessage("Gelişmiş Diyaloglar Modu Yüklendi!", Colors.Green));
        }

        protected override void OnGameStart(Game game, IGameStarter gameStarter)
        {
            if (game.GameType is Campaign)
            {
                CampaignGameStarter campaignStarter = (CampaignGameStarter)gameStarter;
                
                // Sistemleri başlat
                _dialogSistemi = new GelismisDialogSistemi();
                _dusmanSistemi = new DusmanDiyalogSistemi();
                _kulturSistemi = new KulturDiyalogSistemi();
                _beceriSistemi = new BeceriDiyalogSistemi();
                _bolgeSistemi = new BolgeDiyalogSistemi();
                _rastgeleOlaySistemi = new RastgeleOlayDiyalogSistemi();
                
                this.AddDialogs(campaignStarter);
            }
        }

        protected void AddDialogs(CampaignGameStarter campaignStarter)
        {
            // Ana diyalog sistemleri
            _dialogSistemi.AddDialogs(campaignStarter);
            _dusmanSistemi.AddDialogs(campaignStarter);
            _kulturSistemi.AddDialogs(campaignStarter);
            _beceriSistemi.AddDialogs(campaignStarter);
            _bolgeSistemi.AddDialogs(campaignStarter);
            _rastgeleOlaySistemi.AddDialogs(campaignStarter);
            
            // Ek özel diyaloglar
            this.AddOzelDiyaloglar(campaignStarter);
        }

        private void AddOzelDiyaloglar(CampaignGameStarter campaignStarter)
        {
            // Zaman bazlı diyaloglar
            this.AddZamanBazliDiyaloglar(campaignStarter);
            
            // Mevsim bazlı diyaloglar
            this.AddMevsimDiyaloglari(campaignStarter);
            
            // Ruh hali diyalogları
            this.AddRuhHaliDiyaloglari(campaignStarter);
        }

        private void AddZamanBazliDiyaloglar(CampaignGameStarter campaignStarter)
        {
            // Sabah diyalogları
            campaignStarter.AddDialogLine("zaman_sabah", "start", "player_options",
                "Günaydın {PLAYER.NAME}! Erken kalkan yol alır derler.",
                () => CharacterObject.OneToOneConversationCharacter != null && 
                      CampaignTime.Now.GetHourOfDay >= 6 && 
                      CampaignTime.Now.GetHourOfDay < 12 &&
                      MBRandom.RandomInt(0, 4) == 0,
                null);

            // Öğle diyalogları
            campaignStarter.AddDialogLine("zaman_ogle", "start", "player_options",
                "Öğle vakti ho�� geldiniz {PLAYER.NAME}!",
                () => CharacterObject.OneToOneConversationCharacter != null && 
                      CampaignTime.Now.GetHourOfDay >= 12 && 
                      CampaignTime.Now.GetHourOfDay < 18 &&
                      MBRandom.RandomInt(0, 4) == 0,
                null);

            // Akşam diyalogları
            campaignStarter.AddDialogLine("zaman_aksam", "start", "player_options",
                "İyi akşamlar {PLAYER.NAME}! Günün yorgunluğu üzerinizde.",
                () => CharacterObject.OneToOneConversationCharacter != null && 
                      CampaignTime.Now.GetHourOfDay >= 18 && 
                      CampaignTime.Now.GetHourOfDay < 22 &&
                      MBRandom.RandomInt(0, 4) == 0,
                null);

            // Gece diyalogları
            campaignStarter.AddDialogLine("zaman_gece", "start", "player_options",
                "Gece vakti burada olmak cesaret ister, {PLAYER.NAME}!",
                () => CharacterObject.OneToOneConversationCharacter != null && 
                      (CampaignTime.Now.GetHourOfDay >= 22 || CampaignTime.Now.GetHourOfDay < 6) &&
                      MBRandom.RandomInt(0, 4) == 0,
                null);
        }

        private void AddMevsimDiyaloglari(CampaignGameStarter campaignStarter)
        {
            // İlkbahar
            campaignStarter.AddDialogLine("mevsim_ilkbahar", "start", "player_options",
                "İlkbaharın taze havası sizinle gelmiş, {PLAYER.NAME}!",
                () => CharacterObject.OneToOneConversationCharacter != null && 
                      CampaignTime.Now.GetSeasonOfYear == CampaignTime.Seasons.Spring &&
                      MBRandom.RandomInt(0, 5) == 0,
                null);

            // Yaz
            campaignStarter.AddDialogLine("mevsim_yaz", "start", "player_options",
                "Yazın sıcaklığı kadar ateşli görünüyorsunuz, {PLAYER.NAME}!",
                () => CharacterObject.OneToOneConversationCharacter != null && 
                      CampaignTime.Now.GetSeasonOfYear == CampaignTime.Seasons.Summer &&
                      MBRandom.RandomInt(0, 5) == 0,
                null);

            // Sonbahar
            campaignStarter.AddDialogLine("mevsim_sonbahar", "start", "player_options",
                "Sonbahar yaprakları gibi bilge görünüyorsunuz, {PLAYER.NAME}.",
                () => CharacterObject.OneToOneConversationCharacter != null && 
                      CampaignTime.Now.GetSeasonOfYear == CampaignTime.Seasons.Autumn &&
                      MBRandom.RandomInt(0, 5) == 0,
                null);

            // Kış
            campaignStarter.AddDialogLine("mevsim_kis", "start", "player_options",
                "Kış soğuğuna rağmen yüreğiniz sıcak, {PLAYER.NAME}!",
                () => CharacterObject.OneToOneConversationCharacter != null && 
                      CampaignTime.Now.GetSeasonOfYear == CampaignTime.Seasons.Winter &&
                      MBRandom.RandomInt(0, 5) == 0,
                null);
        }

        private void AddRuhHaliDiyaloglari(CampaignGameStarter campaignStarter)
        {
            // Mutlu ruh hali
            campaignStarter.AddDialogLine("ruh_mutlu", "start", "player_options",
                "Yüzünüzdeki gülümseme bulaşıcı, {PLAYER.NAME}!",
                () => CharacterObject.OneToOneConversationCharacter != null && 
                      Hero.MainHero.PartyBelongedTo?.Morale > 70 &&
                      MBRandom.RandomInt(0, 6) == 0,
                null);

            // Üzgün ruh hali
            campaignStarter.AddDialogLine("ruh_uzgun", "start", "player_options",
                "Biraz üzgün görünüyorsunuz {PLAYER.NAME}, yardım edebilir miyim?",
                () => CharacterObject.OneToOneConversationCharacter != null && 
                      Hero.MainHero.PartyBelongedTo?.Morale < 30 &&
                      MBRandom.RandomInt(0, 6) == 0,
                null);

            // Yorgun ruh hali
            campaignStarter.AddDialogLine("ruh_yorgun", "start", "player_options",
                "Dinlenmeye ihtiyacınız var galiba, {PLAYER.NAME}!",
                () => CharacterObject.OneToOneConversationCharacter != null && 
                      Hero.MainHero.PartyBelongedTo?.Food < 5 &&
                      MBRandom.RandomInt(0, 6) == 0,
                null);
        }
    }
}