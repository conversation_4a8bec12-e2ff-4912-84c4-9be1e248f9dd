using TaleWorlds.CampaignSystem;
using TaleWorlds.CampaignSystem.Conversation;
using TaleWorlds.Core;
using TaleWorlds.Library;
using TaleWorlds.CampaignSystem.Actions;
using TaleWorlds.CampaignSystem.CharacterDevelopment;

namespace GelismisDialoglar
{
    public class RastgeleOlayDiyalogSistemi
    {
        private readonly string[] sansliOlaylar = {
            "Vay be! {PLAYER.NAME}, bugün şanslı gününüz! Yolda altın buldum, size hediye ediyorum!",
            "Müjde {PLAYER.NAME}! Tüccarlar sizin hakkınızda güzel şeyler söylüyor. İşte bir hediye!",
            "Harika haber {PLAYER.NAME}! Köylüler sizin için para toplamış. Buyurun!",
            "Şanslısınız {PLAYER.NAME}! Bugün herkes size hayran. İşte bir armağan!",
            "Muhteşem {PLAYER.NAME}! Ünün<PERSON>z sayesinde biri size altın bağışladı!"
        };

        private readonly string[] sanssizOlaylar = {
            "Üzgünüm {PLAYER.NAME}, kötü haberlerim var. Eşkıyalar bölgede aktif.",
            "Dikkat {PLAYER.NAME}! Son zamanlarda hırsızlık olayları arttı.",
            "Maalesef {PLAYER.NAME}, ticaret yolları güvenli değil şu sıralar.",
            "Kötü haber {PLAYER.NAME}! Hastalık salgını var, dikkatli olun.",
            "Üzücü {PLAYER.NAME}! Kuraklık nedeniyle fiyatlar arttı."
        };

        private readonly string[] komikOlaylar = {
            "Haha {PLAYER.NAME}! Az önce komik bir olay oldu. Bir köylü atından düştü!",
            "Gülmekten kırıldım {PLAYER.NAME}! Tüccar mallarını karıştırdı, çok komikti!",
            "Çok eğlenceli {PLAYER.NAME}! İki köpek kavga etti, herkes güldü!",
            "Komik {PLAYER.NAME}! Yaşlı adam gençlere akıl vermeye çalışıyor!",
            "Eğlenceli {PLAYER.NAME}! Çocuklar savaşçı oyunu oynuyor, sizi taklit ediyorlar!"
        };

        private readonly string[] dramatikOlaylar = {
            "Ciddi haber {PLAYER.NAME}! Savaş yaklaşıyor, herkes endişeli.",
            "Dramatik gelişme {PLAYER.NAME}! Kral hastalanmış, saray karışık.",
            "Önemli haber {PLAYER.NAME}! Büyük bir savaş olacakmış.",
            "Ciddi durum {PLAYER.NAME}! Düşmanlar sınırlarda görüldü.",
            "Dramatik {PLAYER.NAME}! Büyük bir karar verilmesi gerekiyor."
        };

        private readonly string[] gizemliOlaylar = {
            "Garip {PLAYER.NAME}! Gece tuhaf sesler duyuldu, kimse açıklayamıyor.",
            "Gizemli {PLAYER.NAME}! Ormanda garip ışıklar görüldü.",
            "Tuhaf {PLAYER.NAME}! Hayvanlar garip davranıyor son günlerde.",
            "Esrarengiz {PLAYER.NAME}! Yaşlılar eski efsanelerden bahsediyor.",
            "Gizemli {PLAYER.NAME}! Rüyalarda garip şeyler görülüyormuş."
        };

        private readonly string[] genelDedikodular = {
            "Duyduğunuza göre {PLAYER.NAME}, Lord X ile Lady Y arasında aşk var!",
            "Söylentiye göre {PLAYER.NAME}, gizli bir hazine varmış!",
            "Dedikodulara göre {PLAYER.NAME}, büyük bir plan hazırlanıyormuş!",
            "Rivayet {PLAYER.NAME}, eski dostlar düşman olmuş!",
            "Kulaktan kulağa {PLAYER.NAME}, büyük değişiklikler geliyor!"
        };

        private readonly string[] komikDedikodular = {
            "Komik dedikodu {PLAYER.NAME}! Tüccar karısından korkuyormuş!",
            "Eğlenceli haber {PLAYER.NAME}! Yaşlı lord dans etmeyi öğreniyormuş!",
            "Gülünç {PLAYER.NAME}! Soylu biri köylü kızına aşık olmuş!",
            "Komik {PLAYER.NAME}! Kral gizlice tatlı yapıyormuş!",
            "Eğlenceli {PLAYER.NAME}! Askerler gizlice şarkı söylüyormuş!"
        };

        private readonly string[] ciddiDedikodular = {
            "Ciddi dedikodu {PLAYER.NAME}! Savaş planları hazırlanıyormuş!",
            "Önemli haber {PLAYER.NAME}! Gizli ittifaklar kuruluyormuş!",
            "Ciddi {PLAYER.NAME}! Büyük bir tehlike yaklaşıyormuş!",
            "Önemli dedikodu {PLAYER.NAME}! Krallık değişecekmiş!",
            "Ciddi haber {PLAYER.NAME}! Düşmanlar plan yapıyormuş!"
        };

        public void AddDialogs(CampaignGameStarter campaignStarter)
        {
            this.AddSansliOlayDialoglari(campaignStarter);
            this.AddSanssizOlayDialoglari(campaignStarter);
            this.AddKomikOlayDialoglari(campaignStarter);
            this.AddDramatikOlayDialoglari(campaignStarter);
            this.AddGizemliOlayDialoglari(campaignStarter);
            this.AddDedikodularDialoglari(campaignStarter);
        }

        private void AddSansliOlayDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < sansliOlaylar.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"rastgele_sansli_{i}", "start", "rastgele_secenekleri",
                    sansliOlaylar[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          MBRandom.RandomInt(0, 100) < 5 && // %5 şans
                          MBRandom.RandomInt(0, sansliOlaylar.Length) == index,
                    null);
            }

            // Şanslı olay seçenekleri
            campaignStarter.AddPlayerLine("sansli_tesekkur", "rastgele_secenekleri", "sansli_cevap",
                "Çok teşekkür ederim! Ne kadar naziksiniz!", null, null);

            campaignStarter.AddDialogLine("sansli_cevap", "sansli_cevap", "rastgele_secenekleri",
                "Rica ederim! Sizin gibi kahramanlar bunu hak ediyor!",
                null, () => SansliOlayOdulu());

            campaignStarter.AddPlayerLine("rastgele_cikis", "rastgele_secenekleri", "close_window",
                "Teşekkürler!", null, null);
        }

        private void AddSanssizOlayDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < sanssizOlaylar.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"rastgele_sanssiz_{i}", "start", "rastgele_secenekleri",
                    sanssizOlaylar[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          MBRandom.RandomInt(0, 100) < 3 && // %3 şans
                          MBRandom.RandomInt(0, sanssizOlaylar.Length) == index,
                    null);
            }

            // Şanssız olay seçenekleri
            campaignStarter.AddPlayerLine("sanssiz_cevap", "rastgele_secenekleri", "sanssiz_tepki",
                "Uyardığınız için teşekkürler.", null, null);

            campaignStarter.AddDialogLine("sanssiz_tepki", "sanssiz_tepki", "rastgele_secenekleri",
                "Dikkatli olun! Sizin gibi değerli insanların başına bir şey gelmesini istemeyiz.",
                null, () => SanssizOlayEtkisi());
        }

        private void AddKomikOlayDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < komikOlaylar.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"rastgele_komik_{i}", "start", "rastgele_secenekleri",
                    komikOlaylar[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          MBRandom.RandomInt(0, 100) < 7 && // %7 şans
                          MBRandom.RandomInt(0, komikOlaylar.Length) == index,
                    null);
            }

            // Komik olay seçenekleri
            campaignStarter.AddPlayerLine("komik_cevap", "rastgele_secenekleri", "komik_tepki",
                "Haha! Gerçekten komikmiş!", null, null);

            campaignStarter.AddDialogLine("komik_tepki", "komik_tepki", "rastgele_secenekleri",
                "Değil mi? Bazen hayat çok eğlenceli olabiliyor!",
                null, () => KomikOlayOdulu());
        }

        private void AddDramatikOlayDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < dramatikOlaylar.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"rastgele_dramatik_{i}", "start", "rastgele_secenekleri",
                    dramatikOlaylar[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          MBRandom.RandomInt(0, 100) < 4 && // %4 şans
                          MBRandom.RandomInt(0, dramatikOlaylar.Length) == index,
                    null);
            }

            // Dramatik olay seçenekleri
            campaignStarter.AddPlayerLine("dramatik_cevap", "rastgele_secenekleri", "dramatik_tepki",
                "Bu gerçekten ciddi bir durum.", null, null);

            campaignStarter.AddDialogLine("dramatik_tepki", "dramatik_tepki", "rastgele_secenekleri",
                "Evet, zor zamanlar geliyor. Sizin gibi liderler gerekli.",
                null, () => DramatikOlayOdulu());
        }

        private void AddGizemliOlayDialoglari(CampaignGameStarter campaignStarter)
        {
            for (int i = 0; i < gizemliOlaylar.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"rastgele_gizemli_{i}", "start", "rastgele_secenekleri",
                    gizemliOlaylar[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          MBRandom.RandomInt(0, 100) < 2 && // %2 şans
                          MBRandom.RandomInt(0, gizemliOlaylar.Length) == index,
                    null);
            }

            // Gizemli olay seçenekleri
            campaignStarter.AddPlayerLine("gizemli_cevap", "rastgele_secenekleri", "gizemli_tepki",
                "Çok ilginç... Araştırmak lazım.", null, null);

            campaignStarter.AddDialogLine("gizemli_tepki", "gizemli_tepki", "rastgele_secenekleri",
                "Evet, çok tuhaf. Belki siz çözebilirsiniz bu gizemi.",
                null, () => GizemliOlayOdulu());
        }

        private void AddDedikodularDialoglari(CampaignGameStarter campaignStarter)
        {
            // Genel dedikodular
            for (int i = 0; i < genelDedikodular.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"dedikodu_genel_{i}", "start", "rastgele_secenekleri",
                    genelDedikodular[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          MBRandom.RandomInt(0, 100) < 8 && // %8 şans
                          MBRandom.RandomInt(0, genelDedikodular.Length) == index,
                    null);
            }

            // Komik dedikodular
            for (int i = 0; i < komikDedikodular.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"dedikodu_komik_{i}", "start", "rastgele_secenekleri",
                    komikDedikodular[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          MBRandom.RandomInt(0, 100) < 6 && // %6 şans
                          MBRandom.RandomInt(0, komikDedikodular.Length) == index,
                    null);
            }

            // Ciddi dedikodular
            for (int i = 0; i < ciddiDedikodular.Length; i++)
            {
                int index = i;
                campaignStarter.AddDialogLine($"dedikodu_ciddi_{i}", "start", "rastgele_secenekleri",
                    ciddiDedikodular[i],
                    () => CharacterObject.OneToOneConversationCharacter != null && 
                          MBRandom.RandomInt(0, 100) < 4 && // %4 şans
                          MBRandom.RandomInt(0, ciddiDedikodular.Length) == index,
                    null);
            }

            // Dedikodu seçenekleri
            campaignStarter.AddPlayerLine("dedikodu_cevap", "rastgele_secenekleri", "dedikodu_tepki",
                "İlginç dedikodular bunlar!", null, null);

            campaignStarter.AddDialogLine("dedikodu_tepki", "dedikodu_tepki", "rastgele_secenekleri",
                "Değil mi? Kulaktan kulağa yayılıyor bu haberler.",
                null, () => DedikodularOdulu());
        }

        // Olay etkisi metodları
        private void SansliOlayOdulu()
        {
            int xpMiktari = MBRandom.RandomInt(30, 60);

            Hero.MainHero.AddSkillXp(DefaultSkills.Charm, xpMiktari);

            if (Hero.MainHero.PartyBelongedTo != null)
            {
                Hero.MainHero.PartyBelongedTo.RecentEventsMorale += 10;
            }
        }

        private void SanssizOlayEtkisi()
        {
            // Küçük moral kaybı
            if (Hero.MainHero.PartyBelongedTo != null)
            {
                Hero.MainHero.PartyBelongedTo.RecentEventsMorale -= 5;
            }
            
            // Ama bilgi için XP
            Hero.MainHero.AddSkillXp(DefaultSkills.Scouting, 20);
        }

        private void KomikOlayOdulu()
        {
            Hero.MainHero.AddSkillXp(DefaultSkills.Charm, 25);
            
            if (Hero.MainHero.PartyBelongedTo != null)
            {
                Hero.MainHero.PartyBelongedTo.RecentEventsMorale += 5;
            }
        }

        private void DramatikOlayOdulu()
        {
            Hero.MainHero.AddSkillXp(DefaultSkills.Leadership, 30);
        }

        private void GizemliOlayOdulu()
        {
            Hero.MainHero.AddSkillXp(DefaultSkills.Scouting, 40);
            Hero.MainHero.AddSkillXp(DefaultSkills.Medicine, 20);
        }

        private void DedikodularOdulu()
        {
            Hero.MainHero.AddSkillXp(DefaultSkills.Charm, 15);
            Hero.MainHero.AddSkillXp(DefaultSkills.Roguery, 10);
        }
    }
}