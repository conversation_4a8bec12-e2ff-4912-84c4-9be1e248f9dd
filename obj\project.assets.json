{"version": 3, "targets": {".NETFramework,Version=v4.7.2": {"Microsoft.NETFramework.ReferenceAssemblies/1.0.3": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies.net472": "1.0.3"}}, "Microsoft.NETFramework.ReferenceAssemblies.net472/1.0.3": {"type": "package", "build": {"build/Microsoft.NETFramework.ReferenceAssemblies.net472.targets": {}}}}}, "libraries": {"Microsoft.NETFramework.ReferenceAssemblies/1.0.3": {"sha512": "vUc9Npcs14QsyOD01tnv/m8sQUnGTGOw1BCmKcv77LBJY7OxhJ+zJF7UD/sCL3lYNFuqmQEVlkfS4Quif6FyYg==", "type": "package", "path": "microsoft.netframework.referenceassemblies/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "microsoft.netframework.referenceassemblies.1.0.3.nupkg.sha512", "microsoft.netframework.referenceassemblies.nuspec"]}, "Microsoft.NETFramework.ReferenceAssemblies.net472/1.0.3": {"sha512": "0E7evZXHXaDYYiLRfpyXvCh+yzM2rNTyuZDI+ZO7UUqSc6GfjePiXTdqJGtgIKUwdI81tzQKmaWprnUiPj9hAw==", "type": "package", "path": "microsoft.netframework.referenceassemblies.net472/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "build/.NETFramework/v4.7.2/Accessibility.dll", "build/.NETFramework/v4.7.2/Accessibility.xml", "build/.NETFramework/v4.7.2/CustomMarshalers.dll", "build/.NETFramework/v4.7.2/CustomMarshalers.xml", "build/.NETFramework/v4.7.2/Facades/Microsoft.Win32.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.AppContext.dll", "build/.NETFramework/v4.7.2/Facades/System.Collections.Concurrent.dll", "build/.NETFramework/v4.7.2/Facades/System.Collections.NonGeneric.dll", "build/.NETFramework/v4.7.2/Facades/System.Collections.Specialized.dll", "build/.NETFramework/v4.7.2/Facades/System.Collections.dll", "build/.NETFramework/v4.7.2/Facades/System.ComponentModel.Annotations.dll", "build/.NETFramework/v4.7.2/Facades/System.ComponentModel.EventBasedAsync.dll", "build/.NETFramework/v4.7.2/Facades/System.ComponentModel.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.ComponentModel.TypeConverter.dll", "build/.NETFramework/v4.7.2/Facades/System.ComponentModel.dll", "build/.NETFramework/v4.7.2/Facades/System.Console.dll", "build/.NETFramework/v4.7.2/Facades/System.Data.Common.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.Contracts.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.Debug.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.FileVersionInfo.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.Process.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.StackTrace.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.TextWriterTraceListener.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.Tools.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.TraceSource.dll", "build/.NETFramework/v4.7.2/Facades/System.Drawing.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.Dynamic.Runtime.dll", "build/.NETFramework/v4.7.2/Facades/System.Globalization.Calendars.dll", "build/.NETFramework/v4.7.2/Facades/System.Globalization.Extensions.dll", "build/.NETFramework/v4.7.2/Facades/System.Globalization.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.Compression.ZipFile.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.FileSystem.DriveInfo.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.FileSystem.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.FileSystem.Watcher.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.FileSystem.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.IsolatedStorage.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.MemoryMappedFiles.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.Pipes.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.UnmanagedMemoryStream.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.dll", "build/.NETFramework/v4.7.2/Facades/System.Linq.Expressions.dll", "build/.NETFramework/v4.7.2/Facades/System.Linq.Parallel.dll", "build/.NETFramework/v4.7.2/Facades/System.Linq.Queryable.dll", "build/.NETFramework/v4.7.2/Facades/System.Linq.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.Http.Rtc.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.NameResolution.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.NetworkInformation.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.Ping.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.Requests.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.Security.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.Sockets.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.WebHeaderCollection.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.WebSockets.Client.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.WebSockets.dll", "build/.NETFramework/v4.7.2/Facades/System.ObjectModel.dll", "build/.NETFramework/v4.7.2/Facades/System.Reflection.Emit.ILGeneration.dll", "build/.NETFramework/v4.7.2/Facades/System.Reflection.Emit.Lightweight.dll", "build/.NETFramework/v4.7.2/Facades/System.Reflection.Emit.dll", "build/.NETFramework/v4.7.2/Facades/System.Reflection.Extensions.dll", "build/.NETFramework/v4.7.2/Facades/System.Reflection.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.Reflection.dll", "build/.NETFramework/v4.7.2/Facades/System.Resources.Reader.dll", "build/.NETFramework/v4.7.2/Facades/System.Resources.ResourceManager.dll", "build/.NETFramework/v4.7.2/Facades/System.Resources.Writer.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.CompilerServices.VisualC.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.Extensions.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.Handles.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.InteropServices.RuntimeInformation.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.InteropServices.WindowsRuntime.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.InteropServices.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.Numerics.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.Serialization.Formatters.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.Serialization.Json.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.Serialization.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.Serialization.Xml.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.Claims.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.Cryptography.Algorithms.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.Cryptography.Csp.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.Cryptography.Encoding.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.Cryptography.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.Cryptography.X509Certificates.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.Principal.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.SecureString.dll", "build/.NETFramework/v4.7.2/Facades/System.ServiceModel.Duplex.dll", "build/.NETFramework/v4.7.2/Facades/System.ServiceModel.Http.dll", "build/.NETFramework/v4.7.2/Facades/System.ServiceModel.NetTcp.dll", "build/.NETFramework/v4.7.2/Facades/System.ServiceModel.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.ServiceModel.Security.dll", "build/.NETFramework/v4.7.2/Facades/System.Text.Encoding.Extensions.dll", "build/.NETFramework/v4.7.2/Facades/System.Text.Encoding.dll", "build/.NETFramework/v4.7.2/Facades/System.Text.RegularExpressions.dll", "build/.NETFramework/v4.7.2/Facades/System.Threading.Overlapped.dll", "build/.NETFramework/v4.7.2/Facades/System.Threading.Tasks.Parallel.dll", "build/.NETFramework/v4.7.2/Facades/System.Threading.Tasks.dll", "build/.NETFramework/v4.7.2/Facades/System.Threading.Thread.dll", "build/.NETFramework/v4.7.2/Facades/System.Threading.ThreadPool.dll", "build/.NETFramework/v4.7.2/Facades/System.Threading.Timer.dll", "build/.NETFramework/v4.7.2/Facades/System.Threading.dll", "build/.NETFramework/v4.7.2/Facades/System.ValueTuple.dll", "build/.NETFramework/v4.7.2/Facades/System.Xml.ReaderWriter.dll", "build/.NETFramework/v4.7.2/Facades/System.Xml.XDocument.dll", "build/.NETFramework/v4.7.2/Facades/System.Xml.XPath.XDocument.dll", "build/.NETFramework/v4.7.2/Facades/System.Xml.XPath.dll", "build/.NETFramework/v4.7.2/Facades/System.Xml.XmlDocument.dll", "build/.NETFramework/v4.7.2/Facades/System.Xml.XmlSerializer.dll", "build/.NETFramework/v4.7.2/Facades/netstandard.dll", "build/.NETFramework/v4.7.2/ISymWrapper.dll", "build/.NETFramework/v4.7.2/ISymWrapper.xml", "build/.NETFramework/v4.7.2/Microsoft.Activities.Build.dll", "build/.NETFramework/v4.7.2/Microsoft.Activities.Build.xml", "build/.NETFramework/v4.7.2/Microsoft.Build.Conversion.v4.0.dll", "build/.NETFramework/v4.7.2/Microsoft.Build.Conversion.v4.0.xml", "build/.NETFramework/v4.7.2/Microsoft.Build.Engine.dll", "build/.NETFramework/v4.7.2/Microsoft.Build.Engine.xml", "build/.NETFramework/v4.7.2/Microsoft.Build.Framework.dll", "build/.NETFramework/v4.7.2/Microsoft.Build.Framework.xml", "build/.NETFramework/v4.7.2/Microsoft.Build.Tasks.v4.0.dll", "build/.NETFramework/v4.7.2/Microsoft.Build.Tasks.v4.0.xml", "build/.NETFramework/v4.7.2/Microsoft.Build.Utilities.v4.0.dll", "build/.NETFramework/v4.7.2/Microsoft.Build.Utilities.v4.0.xml", "build/.NETFramework/v4.7.2/Microsoft.Build.dll", "build/.NETFramework/v4.7.2/Microsoft.Build.xml", "build/.NETFramework/v4.7.2/Microsoft.CSharp.dll", "build/.NETFramework/v4.7.2/Microsoft.CSharp.xml", "build/.NETFramework/v4.7.2/Microsoft.JScript.dll", "build/.NETFramework/v4.7.2/Microsoft.JScript.xml", "build/.NETFramework/v4.7.2/Microsoft.VisualBasic.Compatibility.Data.dll", "build/.NETFramework/v4.7.2/Microsoft.VisualBasic.Compatibility.Data.xml", "build/.NETFramework/v4.7.2/Microsoft.VisualBasic.Compatibility.dll", "build/.NETFramework/v4.7.2/Microsoft.VisualBasic.Compatibility.xml", "build/.NETFramework/v4.7.2/Microsoft.VisualBasic.dll", "build/.NETFramework/v4.7.2/Microsoft.VisualBasic.xml", "build/.NETFramework/v4.7.2/Microsoft.VisualC.STLCLR.dll", "build/.NETFramework/v4.7.2/Microsoft.VisualC.STLCLR.xml", "build/.NETFramework/v4.7.2/Microsoft.VisualC.dll", "build/.NETFramework/v4.7.2/Microsoft.VisualC.xml", "build/.NETFramework/v4.7.2/PermissionSets/FullTrust.xml", "build/.NETFramework/v4.7.2/PermissionSets/Internet.xml", "build/.NETFramework/v4.7.2/PermissionSets/LocalIntranet.xml", "build/.NETFramework/v4.7.2/PresentationBuildTasks.dll", "build/.NETFramework/v4.7.2/PresentationBuildTasks.xml", "build/.NETFramework/v4.7.2/PresentationCore.dll", "build/.NETFramework/v4.7.2/PresentationCore.xml", "build/.NETFramework/v4.7.2/PresentationFramework.Aero.dll", "build/.NETFramework/v4.7.2/PresentationFramework.Aero.xml", "build/.NETFramework/v4.7.2/PresentationFramework.Aero2.dll", "build/.NETFramework/v4.7.2/PresentationFramework.Aero2.xml", "build/.NETFramework/v4.7.2/PresentationFramework.AeroLite.dll", "build/.NETFramework/v4.7.2/PresentationFramework.AeroLite.xml", "build/.NETFramework/v4.7.2/PresentationFramework.Classic.dll", "build/.NETFramework/v4.7.2/PresentationFramework.Classic.xml", "build/.NETFramework/v4.7.2/PresentationFramework.Luna.dll", "build/.NETFramework/v4.7.2/PresentationFramework.Luna.xml", "build/.NETFramework/v4.7.2/PresentationFramework.Royale.dll", "build/.NETFramework/v4.7.2/PresentationFramework.Royale.xml", "build/.NETFramework/v4.7.2/PresentationFramework.dll", "build/.NETFramework/v4.7.2/PresentationFramework.xml", "build/.NETFramework/v4.7.2/ReachFramework.dll", "build/.NETFramework/v4.7.2/ReachFramework.xml", "build/.NETFramework/v4.7.2/RedistList/FrameworkList.xml", "build/.NETFramework/v4.7.2/System.Activities.Core.Presentation.dll", "build/.NETFramework/v4.7.2/System.Activities.Core.Presentation.xml", "build/.NETFramework/v4.7.2/System.Activities.DurableInstancing.dll", "build/.NETFramework/v4.7.2/System.Activities.DurableInstancing.xml", "build/.NETFramework/v4.7.2/System.Activities.Presentation.dll", "build/.NETFramework/v4.7.2/System.Activities.Presentation.xml", "build/.NETFramework/v4.7.2/System.Activities.dll", "build/.NETFramework/v4.7.2/System.Activities.xml", "build/.NETFramework/v4.7.2/System.AddIn.Contract.dll", "build/.NETFramework/v4.7.2/System.AddIn.Contract.xml", "build/.NETFramework/v4.7.2/System.AddIn.dll", "build/.NETFramework/v4.7.2/System.AddIn.xml", "build/.NETFramework/v4.7.2/System.ComponentModel.Composition.Registration.dll", "build/.NETFramework/v4.7.2/System.ComponentModel.Composition.Registration.xml", "build/.NETFramework/v4.7.2/System.ComponentModel.Composition.dll", "build/.NETFramework/v4.7.2/System.ComponentModel.Composition.xml", "build/.NETFramework/v4.7.2/System.ComponentModel.DataAnnotations.dll", "build/.NETFramework/v4.7.2/System.ComponentModel.DataAnnotations.xml", "build/.NETFramework/v4.7.2/System.Configuration.Install.dll", "build/.NETFramework/v4.7.2/System.Configuration.Install.xml", "build/.NETFramework/v4.7.2/System.Configuration.dll", "build/.NETFramework/v4.7.2/System.Configuration.xml", "build/.NETFramework/v4.7.2/System.Core.dll", "build/.NETFramework/v4.7.2/System.Core.xml", "build/.NETFramework/v4.7.2/System.Data.DataSetExtensions.dll", "build/.NETFramework/v4.7.2/System.Data.DataSetExtensions.xml", "build/.NETFramework/v4.7.2/System.Data.Entity.Design.dll", "build/.NETFramework/v4.7.2/System.Data.Entity.Design.xml", "build/.NETFramework/v4.7.2/System.Data.Entity.dll", "build/.NETFramework/v4.7.2/System.Data.Entity.xml", "build/.NETFramework/v4.7.2/System.Data.Linq.dll", "build/.NETFramework/v4.7.2/System.Data.Linq.xml", "build/.NETFramework/v4.7.2/System.Data.OracleClient.dll", "build/.NETFramework/v4.7.2/System.Data.OracleClient.xml", "build/.NETFramework/v4.7.2/System.Data.Services.Client.dll", "build/.NETFramework/v4.7.2/System.Data.Services.Client.xml", "build/.NETFramework/v4.7.2/System.Data.Services.Design.dll", "build/.NETFramework/v4.7.2/System.Data.Services.Design.xml", "build/.NETFramework/v4.7.2/System.Data.Services.dll", "build/.NETFramework/v4.7.2/System.Data.Services.xml", "build/.NETFramework/v4.7.2/System.Data.SqlXml.dll", "build/.NETFramework/v4.7.2/System.Data.SqlXml.xml", "build/.NETFramework/v4.7.2/System.Data.dll", "build/.NETFramework/v4.7.2/System.Data.xml", "build/.NETFramework/v4.7.2/System.Deployment.dll", "build/.NETFramework/v4.7.2/System.Deployment.xml", "build/.NETFramework/v4.7.2/System.Design.dll", "build/.NETFramework/v4.7.2/System.Design.xml", "build/.NETFramework/v4.7.2/System.Device.dll", "build/.NETFramework/v4.7.2/System.Device.xml", "build/.NETFramework/v4.7.2/System.Diagnostics.Tracing.dll", "build/.NETFramework/v4.7.2/System.Diagnostics.Tracing.xml", "build/.NETFramework/v4.7.2/System.DirectoryServices.AccountManagement.dll", "build/.NETFramework/v4.7.2/System.DirectoryServices.AccountManagement.xml", "build/.NETFramework/v4.7.2/System.DirectoryServices.Protocols.dll", "build/.NETFramework/v4.7.2/System.DirectoryServices.Protocols.xml", "build/.NETFramework/v4.7.2/System.DirectoryServices.dll", "build/.NETFramework/v4.7.2/System.DirectoryServices.xml", "build/.NETFramework/v4.7.2/System.Drawing.Design.dll", "build/.NETFramework/v4.7.2/System.Drawing.Design.xml", "build/.NETFramework/v4.7.2/System.Drawing.dll", "build/.NETFramework/v4.7.2/System.Drawing.xml", "build/.NETFramework/v4.7.2/System.Dynamic.dll", "build/.NETFramework/v4.7.2/System.EnterpriseServices.Thunk.dll", "build/.NETFramework/v4.7.2/System.EnterpriseServices.Wrapper.dll", "build/.NETFramework/v4.7.2/System.EnterpriseServices.dll", "build/.NETFramework/v4.7.2/System.EnterpriseServices.xml", "build/.NETFramework/v4.7.2/System.IO.Compression.FileSystem.dll", "build/.NETFramework/v4.7.2/System.IO.Compression.FileSystem.xml", "build/.NETFramework/v4.7.2/System.IO.Compression.dll", "build/.NETFramework/v4.7.2/System.IO.Compression.xml", "build/.NETFramework/v4.7.2/System.IO.Log.dll", "build/.NETFramework/v4.7.2/System.IO.Log.xml", "build/.NETFramework/v4.7.2/System.IdentityModel.Selectors.dll", "build/.NETFramework/v4.7.2/System.IdentityModel.Selectors.xml", "build/.NETFramework/v4.7.2/System.IdentityModel.Services.dll", "build/.NETFramework/v4.7.2/System.IdentityModel.Services.xml", "build/.NETFramework/v4.7.2/System.IdentityModel.dll", "build/.NETFramework/v4.7.2/System.IdentityModel.xml", "build/.NETFramework/v4.7.2/System.Linq.xml", "build/.NETFramework/v4.7.2/System.Management.Instrumentation.dll", "build/.NETFramework/v4.7.2/System.Management.Instrumentation.xml", "build/.NETFramework/v4.7.2/System.Management.dll", "build/.NETFramework/v4.7.2/System.Management.xml", "build/.NETFramework/v4.7.2/System.Messaging.dll", "build/.NETFramework/v4.7.2/System.Messaging.xml", "build/.NETFramework/v4.7.2/System.Net.Http.WebRequest.dll", "build/.NETFramework/v4.7.2/System.Net.Http.WebRequest.xml", "build/.NETFramework/v4.7.2/System.Net.Http.dll", "build/.NETFramework/v4.7.2/System.Net.Http.xml", "build/.NETFramework/v4.7.2/System.Net.dll", "build/.NETFramework/v4.7.2/System.Net.xml", "build/.NETFramework/v4.7.2/System.Numerics.dll", "build/.NETFramework/v4.7.2/System.Numerics.xml", "build/.NETFramework/v4.7.2/System.Printing.dll", "build/.NETFramework/v4.7.2/System.Printing.xml", "build/.NETFramework/v4.7.2/System.Reflection.Context.dll", "build/.NETFramework/v4.7.2/System.Reflection.Context.xml", "build/.NETFramework/v4.7.2/System.Runtime.Caching.dll", "build/.NETFramework/v4.7.2/System.Runtime.Caching.xml", "build/.NETFramework/v4.7.2/System.Runtime.DurableInstancing.dll", "build/.NETFramework/v4.7.2/System.Runtime.DurableInstancing.xml", "build/.NETFramework/v4.7.2/System.Runtime.Remoting.dll", "build/.NETFramework/v4.7.2/System.Runtime.Remoting.xml", "build/.NETFramework/v4.7.2/System.Runtime.Serialization.Formatters.Soap.dll", "build/.NETFramework/v4.7.2/System.Runtime.Serialization.Formatters.Soap.xml", "build/.NETFramework/v4.7.2/System.Runtime.Serialization.dll", "build/.NETFramework/v4.7.2/System.Runtime.Serialization.xml", "build/.NETFramework/v4.7.2/System.Security.dll", "build/.NETFramework/v4.7.2/System.Security.xml", "build/.NETFramework/v4.7.2/System.ServiceModel.Activation.dll", "build/.NETFramework/v4.7.2/System.ServiceModel.Activation.xml", "build/.NETFramework/v4.7.2/System.ServiceModel.Activities.dll", "build/.NETFramework/v4.7.2/System.ServiceModel.Activities.xml", "build/.NETFramework/v4.7.2/System.ServiceModel.Channels.dll", "build/.NETFramework/v4.7.2/System.ServiceModel.Channels.xml", "build/.NETFramework/v4.7.2/System.ServiceModel.Discovery.dll", "build/.NETFramework/v4.7.2/System.ServiceModel.Discovery.xml", "build/.NETFramework/v4.7.2/System.ServiceModel.Routing.dll", "build/.NETFramework/v4.7.2/System.ServiceModel.Routing.xml", "build/.NETFramework/v4.7.2/System.ServiceModel.Web.dll", "build/.NETFramework/v4.7.2/System.ServiceModel.Web.xml", "build/.NETFramework/v4.7.2/System.ServiceModel.dll", "build/.NETFramework/v4.7.2/System.ServiceModel.xml", "build/.NETFramework/v4.7.2/System.ServiceProcess.dll", "build/.NETFramework/v4.7.2/System.ServiceProcess.xml", "build/.NETFramework/v4.7.2/System.Speech.dll", "build/.NETFramework/v4.7.2/System.Speech.xml", "build/.NETFramework/v4.7.2/System.Threading.Tasks.Dataflow.xml", "build/.NETFramework/v4.7.2/System.Transactions.dll", "build/.NETFramework/v4.7.2/System.Transactions.xml", "build/.NETFramework/v4.7.2/System.Web.Abstractions.dll", "build/.NETFramework/v4.7.2/System.Web.ApplicationServices.dll", "build/.NETFramework/v4.7.2/System.Web.ApplicationServices.xml", "build/.NETFramework/v4.7.2/System.Web.DataVisualization.Design.dll", "build/.NETFramework/v4.7.2/System.Web.DataVisualization.dll", "build/.NETFramework/v4.7.2/System.Web.DataVisualization.xml", "build/.NETFramework/v4.7.2/System.Web.DynamicData.Design.dll", "build/.NETFramework/v4.7.2/System.Web.DynamicData.Design.xml", "build/.NETFramework/v4.7.2/System.Web.DynamicData.dll", "build/.NETFramework/v4.7.2/System.Web.DynamicData.xml", "build/.NETFramework/v4.7.2/System.Web.Entity.Design.dll", "build/.NETFramework/v4.7.2/System.Web.Entity.Design.xml", "build/.NETFramework/v4.7.2/System.Web.Entity.dll", "build/.NETFramework/v4.7.2/System.Web.Entity.xml", "build/.NETFramework/v4.7.2/System.Web.Extensions.Design.dll", "build/.NETFramework/v4.7.2/System.Web.Extensions.Design.xml", "build/.NETFramework/v4.7.2/System.Web.Extensions.dll", "build/.NETFramework/v4.7.2/System.Web.Extensions.xml", "build/.NETFramework/v4.7.2/System.Web.Mobile.dll", "build/.NETFramework/v4.7.2/System.Web.Mobile.xml", "build/.NETFramework/v4.7.2/System.Web.RegularExpressions.dll", "build/.NETFramework/v4.7.2/System.Web.RegularExpressions.xml", "build/.NETFramework/v4.7.2/System.Web.Routing.dll", "build/.NETFramework/v4.7.2/System.Web.Services.dll", "build/.NETFramework/v4.7.2/System.Web.Services.xml", "build/.NETFramework/v4.7.2/System.Web.dll", "build/.NETFramework/v4.7.2/System.Web.xml", "build/.NETFramework/v4.7.2/System.Windows.Controls.Ribbon.dll", "build/.NETFramework/v4.7.2/System.Windows.Controls.Ribbon.xml", "build/.NETFramework/v4.7.2/System.Windows.Forms.DataVisualization.Design.dll", "build/.NETFramework/v4.7.2/System.Windows.Forms.DataVisualization.dll", "build/.NETFramework/v4.7.2/System.Windows.Forms.DataVisualization.xml", "build/.NETFramework/v4.7.2/System.Windows.Forms.dll", "build/.NETFramework/v4.7.2/System.Windows.Forms.xml", "build/.NETFramework/v4.7.2/System.Windows.Input.Manipulations.dll", "build/.NETFramework/v4.7.2/System.Windows.Input.Manipulations.xml", "build/.NETFramework/v4.7.2/System.Windows.Presentation.dll", "build/.NETFramework/v4.7.2/System.Windows.Presentation.xml", "build/.NETFramework/v4.7.2/System.Windows.dll", "build/.NETFramework/v4.7.2/System.Workflow.Activities.dll", "build/.NETFramework/v4.7.2/System.Workflow.Activities.xml", "build/.NETFramework/v4.7.2/System.Workflow.ComponentModel.dll", "build/.NETFramework/v4.7.2/System.Workflow.ComponentModel.xml", "build/.NETFramework/v4.7.2/System.Workflow.Runtime.dll", "build/.NETFramework/v4.7.2/System.Workflow.Runtime.xml", "build/.NETFramework/v4.7.2/System.WorkflowServices.dll", "build/.NETFramework/v4.7.2/System.WorkflowServices.xml", "build/.NETFramework/v4.7.2/System.Xaml.dll", "build/.NETFramework/v4.7.2/System.Xaml.xml", "build/.NETFramework/v4.7.2/System.Xml.Linq.dll", "build/.NETFramework/v4.7.2/System.Xml.Linq.xml", "build/.NETFramework/v4.7.2/System.Xml.Serialization.dll", "build/.NETFramework/v4.7.2/System.Xml.dll", "build/.NETFramework/v4.7.2/System.Xml.xml", "build/.NETFramework/v4.7.2/System.dll", "build/.NETFramework/v4.7.2/System.xml", "build/.NETFramework/v4.7.2/UIAutomationClient.dll", "build/.NETFramework/v4.7.2/UIAutomationClient.xml", "build/.NETFramework/v4.7.2/UIAutomationClientsideProviders.dll", "build/.NETFramework/v4.7.2/UIAutomationClientsideProviders.xml", "build/.NETFramework/v4.7.2/UIAutomationProvider.dll", "build/.NETFramework/v4.7.2/UIAutomationProvider.xml", "build/.NETFramework/v4.7.2/UIAutomationTypes.dll", "build/.NETFramework/v4.7.2/UIAutomationTypes.xml", "build/.NETFramework/v4.7.2/WindowsBase.dll", "build/.NETFramework/v4.7.2/WindowsBase.xml", "build/.NETFramework/v4.7.2/WindowsFormsIntegration.dll", "build/.NETFramework/v4.7.2/WindowsFormsIntegration.xml", "build/.NETFramework/v4.7.2/XamlBuildTask.dll", "build/.NETFramework/v4.7.2/XamlBuildTask.xml", "build/.NETFramework/v4.7.2/mscorlib.dll", "build/.NETFramework/v4.7.2/mscorlib.xml", "build/.NETFramework/v4.7.2/namespaces.xml", "build/.NETFramework/v4.7.2/sysglobl.dll", "build/.NETFramework/v4.7.2/sysglobl.xml", "build/Microsoft.NETFramework.ReferenceAssemblies.net472.targets", "microsoft.netframework.referenceassemblies.net472.1.0.3.nupkg.sha512", "microsoft.netframework.referenceassemblies.net472.nuspec"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.7.2": ["Microsoft.NETFramework.ReferenceAssemblies >= 1.0.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Program Files\\Epic Games\\MountAndBlade2\\Modules\\GelismisDialoglar\\GelismisDialoglar.csproj", "projectName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectPath": "C:\\Program Files\\Epic Games\\MountAndBlade2\\Modules\\GelismisDialoglar\\GelismisDialoglar.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Program Files\\Epic Games\\MountAndBlade2\\Modules\\GelismisDialoglar\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net472"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"targetAlias": "net472", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net472": {"targetAlias": "net472", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}